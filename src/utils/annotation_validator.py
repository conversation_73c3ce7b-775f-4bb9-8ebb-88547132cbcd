# -*- coding: utf-8 -*-
from typing import List, Dict, Any, Tuple, Optional
import re
from dataclasses import dataclass
from enum import Enum


class ValidationLevel(Enum):
    """验证级别"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"


@dataclass
class ValidationResult:
    """验证结果"""
    level: ValidationLevel
    message: str
    annotation_index: Optional[int] = None
    field: Optional[str] = None
    suggestion: Optional[str] = None


class AnnotationValidator:
    """标注数据验证器"""
    
    def __init__(self):
        self.min_description_length = 5
        self.max_description_length = 500
        self.min_duration_frames = 1
        self.max_duration_frames = 10000
        self.required_fields = ['start_frame', 'end_frame', 'description']
        
        # 动作词汇库（用于内容验证）
        self.action_keywords = {
            '基本动作': ['走', '跑', '站', '坐', '躺', '蹲', '跳', '爬'],
            '手部动作': ['拿', '放', '推', '拉', '抓', '握', '指', '挥', '拍', '敲'],
            '交互动作': ['说', '听', '看', '摸', '碰', '握手', '拥抱', '交谈'],
            '工具使用': ['写', '画', '切', '组装', '操作', '使用', '打开', '关闭'],
            '运动动作': ['踢', '投', '接', '游', '骑', '滑', '跳跃', '奔跑']
        }
        
    def validate_annotations(self, annotations: List[Dict[str, Any]], 
                           total_frames: int = None) -> List[ValidationResult]:
        """
        验证标注数据
        
        Args:
            annotations: 标注数据列表
            total_frames: 视频总帧数
            
        Returns:
            验证结果列表
        """
        results = []
        
        if not annotations:
            results.append(ValidationResult(
                ValidationLevel.WARNING,
                "没有找到任何标注数据",
                suggestion="请添加至少一个时间窗口标注"
            ))
            return results
        
        # 基本结构验证
        results.extend(self._validate_structure(annotations))
        
        # 时间范围验证
        results.extend(self._validate_time_ranges(annotations, total_frames))
        
        # 内容质量验证
        results.extend(self._validate_content_quality(annotations))
        
        # 重叠检测
        results.extend(self._validate_overlaps(annotations))
        
        # 覆盖率分析
        if total_frames:
            results.extend(self._validate_coverage(annotations, total_frames))
        
        # 一致性检查
        results.extend(self._validate_consistency(annotations))
        
        return results
    
    def _validate_structure(self, annotations: List[Dict]) -> List[ValidationResult]:
        """验证数据结构"""
        results = []
        
        for i, annotation in enumerate(annotations):
            # 检查必需字段
            for field in self.required_fields:
                if field not in annotation:
                    results.append(ValidationResult(
                        ValidationLevel.ERROR,
                        f"缺少必需字段: {field}",
                        annotation_index=i,
                        field=field,
                        suggestion=f"请为标注 {i+1} 添加 {field} 字段"
                    ))
                elif annotation[field] is None:
                    results.append(ValidationResult(
                        ValidationLevel.ERROR,
                        f"字段 {field} 不能为空",
                        annotation_index=i,
                        field=field
                    ))
            
            # 检查数据类型
            if 'start_frame' in annotation:
                if not isinstance(annotation['start_frame'], int):
                    results.append(ValidationResult(
                        ValidationLevel.ERROR,
                        "start_frame 必须是整数",
                        annotation_index=i,
                        field='start_frame'
                    ))
                elif annotation['start_frame'] < 0:
                    results.append(ValidationResult(
                        ValidationLevel.ERROR,
                        "start_frame 不能为负数",
                        annotation_index=i,
                        field='start_frame'
                    ))
            
            if 'end_frame' in annotation:
                if not isinstance(annotation['end_frame'], int):
                    results.append(ValidationResult(
                        ValidationLevel.ERROR,
                        "end_frame 必须是整数",
                        annotation_index=i,
                        field='end_frame'
                    ))
                elif annotation['end_frame'] < 0:
                    results.append(ValidationResult(
                        ValidationLevel.ERROR,
                        "end_frame 不能为负数",
                        annotation_index=i,
                        field='end_frame'
                    ))
        
        return results
    
    def _validate_time_ranges(self, annotations: List[Dict], 
                            total_frames: int = None) -> List[ValidationResult]:
        """验证时间范围"""
        results = []
        
        for i, annotation in enumerate(annotations):
            start = annotation.get('start_frame')
            end = annotation.get('end_frame')
            
            if start is not None and end is not None:
                # 检查起始帧是否小于结束帧
                if start >= end:
                    results.append(ValidationResult(
                        ValidationLevel.ERROR,
                        f"起始帧 ({start}) 必须小于结束帧 ({end})",
                        annotation_index=i,
                        suggestion="请调整时间范围"
                    ))
                
                # 检查持续时间
                duration = end - start + 1
                if duration < self.min_duration_frames:
                    results.append(ValidationResult(
                        ValidationLevel.WARNING,
                        f"时间窗口太短 ({duration} 帧)，可能不足以描述完整动作",
                        annotation_index=i,
                        suggestion=f"建议至少 {self.min_duration_frames} 帧"
                    ))
                elif duration > self.max_duration_frames:
                    results.append(ValidationResult(
                        ValidationLevel.WARNING,
                        f"时间窗口太长 ({duration} 帧)，建议分解为多个动作",
                        annotation_index=i,
                        suggestion=f"建议不超过 {self.max_duration_frames} 帧"
                    ))
                
                # 检查是否超出视频范围
                if total_frames:
                    if end >= total_frames:
                        results.append(ValidationResult(
                            ValidationLevel.ERROR,
                            f"结束帧 ({end}) 超出视频范围 (0-{total_frames-1})",
                            annotation_index=i,
                            field='end_frame'
                        ))
        
        return results
    
    def _validate_content_quality(self, annotations: List[Dict]) -> List[ValidationResult]:
        """验证内容质量"""
        results = []
        
        for i, annotation in enumerate(annotations):
            description = annotation.get('description', '')
            
            if not description or not description.strip():
                results.append(ValidationResult(
                    ValidationLevel.ERROR,
                    "描述不能为空",
                    annotation_index=i,
                    field='description',
                    suggestion="请添加动作描述"
                ))
                continue
            
            description = description.strip()
            
            # 长度检查
            if len(description) < self.min_description_length:
                results.append(ValidationResult(
                    ValidationLevel.WARNING,
                    f"描述过短 ({len(description)} 字符)，建议更详细",
                    annotation_index=i,
                    field='description',
                    suggestion=f"建议至少 {self.min_description_length} 字符"
                ))
            elif len(description) > self.max_description_length:
                results.append(ValidationResult(
                    ValidationLevel.WARNING,
                    f"描述过长 ({len(description)} 字符)，建议简化",
                    annotation_index=i,
                    field='description',
                    suggestion=f"建议不超过 {self.max_description_length} 字符"
                ))
            
            # 内容质量检查
            if description.lower() in ['无', 'none', 'n/a', '待填写', 'todo']:
                results.append(ValidationResult(
                    ValidationLevel.WARNING,
                    "描述内容需要完善",
                    annotation_index=i,
                    field='description',
                    suggestion="请提供具体的动作描述"
                ))
            
            # 检查是否包含动作词汇
            has_action_keyword = False
            for category, keywords in self.action_keywords.items():
                if any(keyword in description for keyword in keywords):
                    has_action_keyword = True
                    break
            
            if not has_action_keyword:
                results.append(ValidationResult(
                    ValidationLevel.INFO,
                    "建议在描述中包含具体的动作词汇",
                    annotation_index=i,
                    field='description',
                    suggestion="如：走路、拿取、说话等"
                ))
            
            # 检查重复词汇
            words = description.split()
            if len(words) != len(set(words)) and len(words) > 3:
                results.append(ValidationResult(
                    ValidationLevel.INFO,
                    "描述中存在重复词汇",
                    annotation_index=i,
                    field='description',
                    suggestion="考虑简化表达"
                ))
        
        return results
    
    def _validate_overlaps(self, annotations: List[Dict]) -> List[ValidationResult]:
        """检测时间重叠"""
        results = []
        
        # 按起始时间排序
        sorted_annotations = sorted(
            enumerate(annotations), 
            key=lambda x: x[1].get('start_frame', 0)
        )
        
        for i in range(len(sorted_annotations) - 1):
            current_idx, current = sorted_annotations[i]
            next_idx, next_ann = sorted_annotations[i + 1]
            
            current_end = current.get('end_frame', 0)
            next_start = next_ann.get('start_frame', 0)
            
            if current_end >= next_start:
                overlap_frames = current_end - next_start + 1
                results.append(ValidationResult(
                    ValidationLevel.WARNING,
                    f"标注 {current_idx+1} 和 {next_idx+1} 存在时间重叠 ({overlap_frames} 帧)",
                    suggestion="考虑调整时间范围或合并相关动作"
                ))
        
        return results
    
    def _validate_coverage(self, annotations: List[Dict], 
                         total_frames: int) -> List[ValidationResult]:
        """分析标注覆盖率"""
        results = []
        
        if not annotations:
            return results
        
        # 计算已标注的帧数
        annotated_frames = set()
        for annotation in annotations:
            start = annotation.get('start_frame', 0)
            end = annotation.get('end_frame', 0)
            annotated_frames.update(range(start, end + 1))
        
        coverage_ratio = len(annotated_frames) / total_frames
        
        if coverage_ratio < 0.1:
            results.append(ValidationResult(
                ValidationLevel.WARNING,
                f"标注覆盖率较低 ({coverage_ratio:.1%})，可能遗漏重要动作",
                suggestion="考虑增加更多时间窗口标注"
            ))
        elif coverage_ratio > 0.9:
            results.append(ValidationResult(
                ValidationLevel.INFO,
                f"标注覆盖率很高 ({coverage_ratio:.1%})，标注较为完整"
            ))
        
        # 检查大的未标注间隙
        all_frames = set(range(total_frames))
        unannotated_frames = all_frames - annotated_frames
        
        if unannotated_frames:
            # 找到连续的未标注区间
            gaps = self._find_consecutive_ranges(sorted(unannotated_frames))
            large_gaps = [gap for gap in gaps if gap[1] - gap[0] + 1 > 100]  # 超过100帧的间隙
            
            for gap_start, gap_end in large_gaps:
                gap_size = gap_end - gap_start + 1
                results.append(ValidationResult(
                    ValidationLevel.INFO,
                    f"发现较大的未标注区间: 帧 {gap_start}-{gap_end} ({gap_size} 帧)",
                    suggestion="检查是否有遗漏的动作需要标注"
                ))
        
        return results
    
    def _validate_consistency(self, annotations: List[Dict]) -> List[ValidationResult]:
        """检查标注一致性"""
        results = []
        
        # 检查描述风格一致性
        descriptions = [ann.get('description', '') for ann in annotations if ann.get('description')]
        
        if len(descriptions) > 1:
            # 检查长度一致性
            lengths = [len(desc) for desc in descriptions]
            avg_length = sum(lengths) / len(lengths)
            
            very_short = [i for i, length in enumerate(lengths) if length < avg_length * 0.3]
            very_long = [i for i, length in enumerate(lengths) if length > avg_length * 3]
            
            if very_short:
                results.append(ValidationResult(
                    ValidationLevel.INFO,
                    f"部分描述明显较短，建议保持风格一致",
                    suggestion="统一描述的详细程度"
                ))
            
            if very_long:
                results.append(ValidationResult(
                    ValidationLevel.INFO,
                    f"部分描述明显较长，建议保持风格一致",
                    suggestion="统一描述的详细程度"
                ))
        
        return results
    
    def _find_consecutive_ranges(self, numbers: List[int]) -> List[Tuple[int, int]]:
        """找到连续数字的范围"""
        if not numbers:
            return []
        
        ranges = []
        start = numbers[0]
        end = numbers[0]
        
        for i in range(1, len(numbers)):
            if numbers[i] == end + 1:
                end = numbers[i]
            else:
                ranges.append((start, end))
                start = end = numbers[i]
        
        ranges.append((start, end))
        return ranges
    
    def get_validation_summary(self, results: List[ValidationResult]) -> Dict[str, Any]:
        """获取验证结果摘要"""
        summary = {
            'total_issues': len(results),
            'errors': len([r for r in results if r.level == ValidationLevel.ERROR]),
            'warnings': len([r for r in results if r.level == ValidationLevel.WARNING]),
            'info': len([r for r in results if r.level == ValidationLevel.INFO]),
            'has_critical_issues': any(r.level == ValidationLevel.ERROR for r in results)
        }
        
        return summary
