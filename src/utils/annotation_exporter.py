# -*- coding: utf-8 -*-
import json
import csv
import xml.etree.ElementTree as ET
from datetime import datetime
from typing import List, Dict, Any, Optional
import os


class AnnotationExporter:
    """标注数据导出器，支持多种格式"""
    
    def __init__(self):
        self.supported_formats = {
            'json': self.export_json,
            'csv': self.export_csv,
            'xml': self.export_xml,
            'txt': self.export_txt,
            'srt': self.export_srt,
            'vtt': self.export_vtt
        }
    
    def export(self, annotations: List[Dict], file_path: str, format_type: str = None, **kwargs) -> bool:
        """
        导出标注数据
        
        Args:
            annotations: 标注数据列表
            file_path: 输出文件路径
            format_type: 格式类型，如果为None则从文件扩展名推断
            **kwargs: 额外参数
            
        Returns:
            是否成功导出
        """
        if format_type is None:
            format_type = self._get_format_from_extension(file_path)
        
        if format_type not in self.supported_formats:
            raise ValueError(f"不支持的格式: {format_type}")
        
        try:
            return self.supported_formats[format_type](annotations, file_path, **kwargs)
        except Exception as e:
            print(f"导出失败: {e}")
            return False
    
    def _get_format_from_extension(self, file_path: str) -> str:
        """从文件扩展名推断格式"""
        ext = os.path.splitext(file_path)[1].lower().lstrip('.')
        return ext if ext in self.supported_formats else 'json'
    
    def export_json(self, annotations: List[Dict], file_path: str, **kwargs) -> bool:
        """导出为JSON格式"""
        export_data = {
            "metadata": {
                "export_time": datetime.now().isoformat(),
                "format_version": "1.0",
                "total_annotations": len(annotations)
            },
            "annotations": annotations
        }
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, ensure_ascii=False, indent=2)
        
        return True
    
    def export_csv(self, annotations: List[Dict], file_path: str, **kwargs) -> bool:
        """导出为CSV格式"""
        if not annotations:
            return False
        
        # 获取所有可能的字段
        all_fields = set()
        for annotation in annotations:
            all_fields.update(annotation.keys())
        
        # 确保基本字段在前面
        basic_fields = ['start_frame', 'end_frame', 'duration_frames', 'description']
        fieldnames = [field for field in basic_fields if field in all_fields]
        fieldnames.extend([field for field in sorted(all_fields) if field not in basic_fields])
        
        with open(file_path, 'w', newline='', encoding='utf-8') as f:
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(annotations)
        
        return True
    
    def export_xml(self, annotations: List[Dict], file_path: str, **kwargs) -> bool:
        """导出为XML格式"""
        root = ET.Element("video_annotations")
        
        # 添加元数据
        metadata = ET.SubElement(root, "metadata")
        ET.SubElement(metadata, "export_time").text = datetime.now().isoformat()
        ET.SubElement(metadata, "total_annotations").text = str(len(annotations))
        
        # 添加标注
        annotations_elem = ET.SubElement(root, "annotations")
        
        for i, annotation in enumerate(annotations):
            ann_elem = ET.SubElement(annotations_elem, "annotation", id=str(i))
            
            for key, value in annotation.items():
                elem = ET.SubElement(ann_elem, key)
                elem.text = str(value) if value is not None else ""
        
        # 格式化XML
        self._indent_xml(root)
        
        tree = ET.ElementTree(root)
        tree.write(file_path, encoding='utf-8', xml_declaration=True)
        
        return True
    
    def export_txt(self, annotations: List[Dict], file_path: str, **kwargs) -> bool:
        """导出为纯文本格式"""
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write("视频动作标注\n")
            f.write("=" * 50 + "\n")
            f.write(f"导出时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"标注总数: {len(annotations)}\n\n")
            
            for i, annotation in enumerate(annotations, 1):
                f.write(f"标注 {i}:\n")
                f.write(f"  时间范围: 帧 {annotation.get('start_frame', 'N/A')} - {annotation.get('end_frame', 'N/A')}\n")
                f.write(f"  持续时间: {annotation.get('duration_frames', 'N/A')} 帧\n")
                f.write(f"  描述: {annotation.get('description', 'N/A')}\n")
                
                # 添加其他字段
                for key, value in annotation.items():
                    if key not in ['start_frame', 'end_frame', 'duration_frames', 'description']:
                        f.write(f"  {key}: {value}\n")
                
                f.write("\n")
        
        return True
    
    def export_srt(self, annotations: List[Dict], file_path: str, fps: float = 25.0, **kwargs) -> bool:
        """导出为SRT字幕格式"""
        with open(file_path, 'w', encoding='utf-8') as f:
            for i, annotation in enumerate(annotations, 1):
                start_frame = annotation.get('start_frame', 0)
                end_frame = annotation.get('end_frame', 0)
                description = annotation.get('description', '')
                
                # 转换帧数为时间
                start_time = self._frame_to_srt_time(start_frame, fps)
                end_time = self._frame_to_srt_time(end_frame, fps)
                
                f.write(f"{i}\n")
                f.write(f"{start_time} --> {end_time}\n")
                f.write(f"{description}\n\n")
        
        return True
    
    def export_vtt(self, annotations: List[Dict], file_path: str, fps: float = 25.0, **kwargs) -> bool:
        """导出为WebVTT格式"""
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write("WEBVTT\n\n")
            
            for annotation in annotations:
                start_frame = annotation.get('start_frame', 0)
                end_frame = annotation.get('end_frame', 0)
                description = annotation.get('description', '')
                
                # 转换帧数为时间
                start_time = self._frame_to_vtt_time(start_frame, fps)
                end_time = self._frame_to_vtt_time(end_frame, fps)
                
                f.write(f"{start_time} --> {end_time}\n")
                f.write(f"{description}\n\n")
        
        return True
    
    def _frame_to_srt_time(self, frame: int, fps: float) -> str:
        """将帧数转换为SRT时间格式 (HH:MM:SS,mmm)"""
        total_seconds = frame / fps
        hours = int(total_seconds // 3600)
        minutes = int((total_seconds % 3600) // 60)
        seconds = int(total_seconds % 60)
        milliseconds = int((total_seconds % 1) * 1000)
        
        return f"{hours:02d}:{minutes:02d}:{seconds:02d},{milliseconds:03d}"
    
    def _frame_to_vtt_time(self, frame: int, fps: float) -> str:
        """将帧数转换为VTT时间格式 (HH:MM:SS.mmm)"""
        total_seconds = frame / fps
        hours = int(total_seconds // 3600)
        minutes = int((total_seconds % 3600) // 60)
        seconds = int(total_seconds % 60)
        milliseconds = int((total_seconds % 1) * 1000)
        
        return f"{hours:02d}:{minutes:02d}:{seconds:02d}.{milliseconds:03d}"
    
    def _indent_xml(self, elem, level=0):
        """格式化XML缩进"""
        i = "\n" + level * "  "
        if len(elem):
            if not elem.text or not elem.text.strip():
                elem.text = i + "  "
            if not elem.tail or not elem.tail.strip():
                elem.tail = i
            for elem in elem:
                self._indent_xml(elem, level + 1)
            if not elem.tail or not elem.tail.strip():
                elem.tail = i
        else:
            if level and (not elem.tail or not elem.tail.strip()):
                elem.tail = i


class AnnotationImporter:
    """标注数据导入器，支持多种格式"""
    
    def __init__(self):
        self.supported_formats = {
            'json': self.import_json,
            'csv': self.import_csv,
            'xml': self.import_xml
        }
    
    def import_annotations(self, file_path: str, format_type: str = None) -> Optional[List[Dict]]:
        """
        导入标注数据
        
        Args:
            file_path: 文件路径
            format_type: 格式类型，如果为None则从文件扩展名推断
            
        Returns:
            标注数据列表，失败时返回None
        """
        if format_type is None:
            format_type = self._get_format_from_extension(file_path)
        
        if format_type not in self.supported_formats:
            raise ValueError(f"不支持的格式: {format_type}")
        
        try:
            return self.supported_formats[format_type](file_path)
        except Exception as e:
            print(f"导入失败: {e}")
            return None
    
    def _get_format_from_extension(self, file_path: str) -> str:
        """从文件扩展名推断格式"""
        ext = os.path.splitext(file_path)[1].lower().lstrip('.')
        return ext if ext in self.supported_formats else 'json'
    
    def import_json(self, file_path: str) -> List[Dict]:
        """从JSON文件导入"""
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 支持新旧格式
        if isinstance(data, list):
            return data
        elif isinstance(data, dict) and 'annotations' in data:
            return data['annotations']
        else:
            raise ValueError("无效的JSON格式")
    
    def import_csv(self, file_path: str) -> List[Dict]:
        """从CSV文件导入"""
        annotations = []
        
        with open(file_path, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                # 转换数值字段
                annotation = {}
                for key, value in row.items():
                    if key in ['start_frame', 'end_frame', 'duration_frames']:
                        try:
                            annotation[key] = int(value) if value else 0
                        except ValueError:
                            annotation[key] = 0
                    else:
                        annotation[key] = value
                
                annotations.append(annotation)
        
        return annotations
    
    def import_xml(self, file_path: str) -> List[Dict]:
        """从XML文件导入"""
        tree = ET.parse(file_path)
        root = tree.getroot()
        
        annotations = []
        
        # 查找annotations元素
        annotations_elem = root.find('annotations')
        if annotations_elem is None:
            annotations_elem = root  # 如果没有annotations包装，直接使用root
        
        for ann_elem in annotations_elem.findall('annotation'):
            annotation = {}
            
            for child in ann_elem:
                key = child.tag
                value = child.text or ""
                
                # 转换数值字段
                if key in ['start_frame', 'end_frame', 'duration_frames']:
                    try:
                        annotation[key] = int(value) if value else 0
                    except ValueError:
                        annotation[key] = 0
                else:
                    annotation[key] = value
            
            annotations.append(annotation)
        
        return annotations
