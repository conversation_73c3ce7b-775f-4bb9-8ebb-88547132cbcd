# -*- coding: utf-8 -*-
from PyQt5.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
    QPushButton, QComboBox, QFileDialog, QMessageBox, 
    QListWidget, QListWidgetItem, QDialog, QLineEdit, 
    QDialogButtonBox, QCheckBox, QScrollArea, QGridLayout,
    QInputDialog
)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QSize, QTextCodec
from PyQt5.QtGui import QKeyEvent, QImage, QPixmap
from typing import Dict, List, Set, Optional, Any, Tuple
import os
import numpy as np
import traceback

from src.core.hdf5_model import HDF5Model
from src.ui.image_window import ImageWindow
from src.ui.timeline_widget import TimelineWidget, TimelineSegment
from src.ui.enhanced_input_dialog import EnhancedInputDialog

# 设置Qt的文本编码
try:
    QTextCodec.setCodecForLocale(QTextCodec.codecForName('UTF-8'))
except:
    pass


class MainWindow(QMainWindow):
    """主窗口"""
    
    def __init__(self):
        """初始化主窗口"""
        super().__init__()
        
        # 设置窗口标题和大小
        self.setWindowTitle("HDF5 Annotation Tool")
        self.resize(1200, 800)
        
        # 设置样式表
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f5f5f5;
            }
            QLabel {
                font-size: 12px;
            }
            QListWidget {
                border: 1px solid #ddd;
                border-radius: 4px;
                background-color: white;
            }
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 6px 12px;
                border-radius: 4px;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:pressed {
                background-color: #3d8b40;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)
        
        # 初始化各种变量
        # 初始化图像窗口字典
        self.image_windows = {}
        
        # 初始化HDF5模型
        self.hdf5_model = None
        
        # 存储当前打开的文件夹路径
        self.current_folder = None
        
        # 存储文件夹中的所有HDF5文件列表
        self.hdf5_files = []
        
        # 当前打开的文件在列表中的索引
        self.current_file_index = -1
        
        # 图像展示区滚动布局
        self.images_scroll_area = None
        self.images_grid_layout = None
        
        # 选中的用于显示段的键
        self.selected_segment_keys = set()
        
        # 当前编辑的language类型键
        self.current_editing_key = "language"
        
        # 初始化当前显示的language内容
        self.current_language = ""
        self.language_value_label = QLabel("无")
        self.language_value_label.setWordWrap(True)
        self.language_value_label.setStyleSheet("""
            background-color: #f0f8ff;
            border: 1px solid #d0e0f0;
            border-radius: 4px;
            padding: 8px;
            font-weight: bold;
            min-height: 20px;
        """)
        
        # 初始化数据显示标签
        self.data_value_label = QLabel("请选择数据键和帧...")
        self.data_value_label.setWordWrap(True)
        self.data_value_label.setStyleSheet("""
            background-color: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 8px;
            min-height: 60px;
        """)
        
        # 创建中央窗口部件
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)
        
        # 创建布局
        self.main_layout = QHBoxLayout(self.central_widget)
        self.main_layout.setContentsMargins(15, 15, 15, 15) # 设置边距
        self.main_layout.setSpacing(15) # 设置间距
        
        # 创建左侧控制面板
        self.create_left_panel()
        
        # 创建右侧显示和控制区域
        self.create_right_panel()
        
        # 创建底部状态栏
        self.statusBar().showMessage("准备就绪")
        
        # 初始化定时器
        self.play_timer = QTimer(self)
        self.play_timer.timeout.connect(self.next_frame)
        
        # 添加窗口大小变化的延迟更新定时器
        self.resize_timer = QTimer(self)
        self.resize_timer.setSingleShot(True)
        self.resize_timer.timeout.connect(self.on_resize_finished)
        
        # 连接时间轴的多选信号
        self.timeline_widget.segmentsMultiSelected.connect(self.on_segments_multi_selected)
    
    def create_left_panel(self):
        """创建左侧控制面板"""
        # 创建左侧面板容器
        left_panel = QWidget()
        left_panel.setFixedWidth(280)
        left_layout = QVBoxLayout(left_panel)
        left_layout.setContentsMargins(0, 0, 0, 0)
        
        # 创建文件控制区域
        file_group = QWidget()
        file_layout = QVBoxLayout(file_group)
        file_layout.setContentsMargins(5, 5, 5, 10)
        
        # 添加文件打开按钮
        file_open_layout = QHBoxLayout()
        self.open_file_btn = QPushButton("打开文件")
        self.open_file_btn.clicked.connect(self.open_file)
        self.open_folder_btn = QPushButton("打开文件夹")
        self.open_folder_btn.clicked.connect(self.open_folder)
        file_open_layout.addWidget(self.open_file_btn)
        file_open_layout.addWidget(self.open_folder_btn)
        file_layout.addLayout(file_open_layout)
        
        # 添加文件列表
        file_list_label = QLabel("文件列表:")
        file_list_label.setStyleSheet("font-weight: bold; margin-top: 5px;")
        file_layout.addWidget(file_list_label)
        
        self.file_list_widget = QListWidget()
        self.file_list_widget.setMinimumHeight(150)
        self.file_list_widget.itemClicked.connect(self.on_file_selected)
        file_layout.addWidget(self.file_list_widget)
        
        # 添加文件导航按钮
        file_nav_layout = QHBoxLayout()
        self.prev_file_btn = QPushButton("上一个文件")
        self.prev_file_btn.clicked.connect(self.prev_file)
        self.prev_file_btn.setEnabled(False)
        self.next_file_btn = QPushButton("下一个文件")
        self.next_file_btn.clicked.connect(self.next_file)
        self.next_file_btn.setEnabled(False)
        file_nav_layout.addWidget(self.prev_file_btn)
        file_nav_layout.addWidget(self.next_file_btn)
        file_layout.addLayout(file_nav_layout)
        
        left_layout.addWidget(file_group)
        
        # 移除Language编辑控制区域 - 根据需求简化界面
        
        # 移除批量设置区域 - 根据需求简化界面
        
        # 创建数据集显示区域（只显示，不可交互）
        data_group = QWidget()
        data_layout = QVBoxLayout(data_group)
        data_layout.setContentsMargins(5, 5, 5, 10)

        # 添加图像数据集显示
        image_label = QLabel("图像数据集:")
        image_label.setStyleSheet("font-weight: bold;")
        data_layout.addWidget(image_label)

        self.image_list_widget = QListWidget()
        self.image_list_widget.setMinimumHeight(80)
        self.image_list_widget.setEnabled(False)  # 禁用交互
        data_layout.addWidget(self.image_list_widget)

        # 添加其他数据集显示
        data_label = QLabel("其他数据集:")
        data_label.setStyleSheet("font-weight: bold; margin-top: 5px;")
        data_layout.addWidget(data_label)

        self.data_list_widget = QListWidget()
        self.data_list_widget.setMinimumHeight(80)
        self.data_list_widget.setEnabled(False)  # 禁用交互
        data_layout.addWidget(self.data_list_widget)

        left_layout.addWidget(data_group)
        
        # 添加到主布局
        self.main_layout.addWidget(left_panel)
    
    def create_right_panel(self):
        """创建右侧显示和控制区域"""
        # 创建右侧面板容器
        right_panel = QWidget()
        right_layout = QVBoxLayout(right_panel)
        right_layout.setContentsMargins(0, 0, 0, 0)
        right_layout.setSpacing(8)  # 减少间距
        
        # 创建当前选中时间窗口显示区域
        current_window_layout = QVBoxLayout()
        self.language_title = QLabel("当前选中时间窗口:")
        self.language_title.setStyleSheet("font-weight: bold; color: #3c8b4b; font-size: 13px;")
        current_window_layout.addWidget(self.language_title)

        # 添加当前时间窗口信息标签
        current_window_layout.addWidget(self.language_value_label)
        right_layout.addLayout(current_window_layout)
        
        # 创建图像显示区域
        images_label = QLabel("图像显示区域:")
        images_label.setStyleSheet("font-weight: bold; margin-bottom: 5px;")
        right_layout.addWidget(images_label)
        
        # 创建滚动区域用于显示图像
        self.images_scroll_area = QScrollArea()
        self.images_scroll_area.setWidgetResizable(True)
        self.images_scroll_area.setMinimumHeight(300)  # 减少最小高度
        
        # 创建容器和网格布局用于放置图像
        images_container = QWidget()
        self.images_grid_layout = QGridLayout(images_container)
        self.images_grid_layout.setContentsMargins(5, 5, 5, 5)
        self.images_grid_layout.setSpacing(10)
        
        self.images_scroll_area.setWidget(images_container)
        # 让图像显示区域占据大部分空间
        right_layout.addWidget(self.images_scroll_area, 3)  # 权重为3
        
        # 创建时间轴和控制区域
        timeline_label = QLabel("时间轴:")
        timeline_label.setStyleSheet("font-weight: bold; margin-top: 5px; margin-bottom: 3px;")
        right_layout.addWidget(timeline_label)
        
        # 创建时间轴小部件
        self.timeline_widget = TimelineWidget()
        self.timeline_widget.frameChanged.connect(self.on_frame_changed)
        self.timeline_widget.rangeSelected.connect(self.on_range_selected)
        self.timeline_widget.windowAdded.connect(self.on_window_added)
        # 设置时间轴的最大高度，让它不占用太多空间
        self.timeline_widget.setMaximumHeight(200)
        self.timeline_widget.setMinimumHeight(120)
        
        # 让时间轴占据较少空间
        right_layout.addWidget(self.timeline_widget, 1)  # 权重为1
        
        # 移除控制按钮区域，因为控制功能已经集成到时间轴组件中
        
        # 添加到主布局
        self.main_layout.addWidget(right_panel, 1)
    
    def open_file(self):
        """打开单个HDF5文件"""
        # 打开文件对话框
        file_path, _ = QFileDialog.getOpenFileName(
            self, "打开HDF5文件", "", "HDF5文件 (*.hdf5 *.h5);;所有文件 (*)"
        )
        
        if file_path:
            # 清除当前文件夹和文件列表状态
            self.current_folder = None
            self.hdf5_files = []
            self.current_file_index = -1
            self.file_list_widget.clear()
            
            # 隐藏批量设置功能区域（单文件模式不需要）
            self.batch_group.hide()
            
            self.load_hdf5_file(file_path)
    
    def open_folder(self):
        """打开文件夹并加载其中的所有HDF5文件"""
        folder_path = QFileDialog.getExistingDirectory(self, "选择包含HDF5文件的文件夹")
        
        if folder_path:
            self.current_folder = folder_path
            self.load_folder_files(folder_path)
    
    def natural_sort_key(self, file_name):
        """
        实现自然排序的键函数，用于对文件名进行排序
        这将同时考虑字母和数字的混合排序，例如 "file1", "file2", "file10" 会正确排序
        同时也能处理形如 "file_A_001", "file_B_002" 这样的混合模式
        
        Args:
            file_name: 文件名
            
        Returns:
            排序键
        """
        import re
        
        # 将文件名拆分为文本和数字部分
        def atoi(text):
            return int(text) if text.isdigit() else text
        
        # 将每个数字和非数字部分分开，并转换数字为整数
        parts = re.split(r'(\d+)', file_name)
        return [atoi(part) for part in parts]
        
    def load_folder_files(self, folder_path):
        """加载文件夹中的所有HDF5文件"""
        # 清除之前的文件列表
        self.file_list_widget.clear()
        self.hdf5_files = []
        
        # 查找文件夹中的所有HDF5文件
        file_names = []
        for file_name in os.listdir(folder_path):
            if file_name.endswith(('.hdf5', '.h5')):
                file_names.append(file_name)
        
        # 使用自然排序对文件名进行排序
        file_names.sort(key=self.natural_sort_key)
        print(f"排序后的文件列表: {file_names}")
        
        # 添加排序后的文件到列表
        for file_name in file_names:
            file_path = os.path.join(folder_path, file_name)
            self.hdf5_files.append(file_path)
            
            # 添加到列表小部件
            item = QListWidgetItem(file_name)
            item.setToolTip(file_path)
            self.file_list_widget.addItem(item)
        
        # 更新状态栏信息
        if self.hdf5_files:
            self.statusBar().showMessage(f"找到 {len(self.hdf5_files)} 个HDF5文件")
            
            # 显示批量设置功能区域
            self.batch_group.show()
            self.batch_execute_btn.setEnabled(True)
            
            # 默认加载第一个文件
            self.current_file_index = 0
            self.file_list_widget.setCurrentRow(0)
            self.load_hdf5_file(self.hdf5_files[0])
            
            # 启用文件导航按钮
            self.update_file_navigation_buttons()
        else:
            self.statusBar().showMessage("文件夹中没有找到HDF5文件")
            # 隐藏批量设置功能区域
            self.batch_group.hide()
    
    def load_hdf5_file(self, file_path):
        """加载HDF5文件"""
        try:
            # 保存当前已添加到时间轴上的键
            previously_selected_keys = set(self.selected_segment_keys)
            print(f"保存当前已添加的键: {previously_selected_keys}")
            
            # 关闭之前的模型
            if self.hdf5_model:
                self.hdf5_model.close()
                
            # 关闭所有图像窗口
            for window in self.image_windows.values():
                window.close()
            self.image_windows = {}
            
            # 创建新的HDF5模型
            self.hdf5_model = HDF5Model(file_path)
            
            # 更新UI
            self.update_ui_with_model()
            
            # 更新状态栏
            file_name = os.path.basename(file_path)
            compression_status = "压缩" if self.hdf5_model.is_compressed() else "非压缩"
            self.statusBar().showMessage(f"已加载文件: {file_name} ({compression_status}数据集)")
            self.setWindowTitle(f"HDF5文件可视化与标注工具 - {file_name}")
            
            # 恢复之前添加的key进度条，如果新文件中存在这些key
            self.restore_selected_keys(previously_selected_keys)
            
            # 默认显示所有图像
            self.display_all_images()
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"无法加载HDF5文件: {e}")
            self.statusBar().showMessage("文件加载失败")
    
    def restore_selected_keys(self, previously_selected_keys):
        """恢复之前添加的key进度条"""
        if not self.hdf5_model or not previously_selected_keys:
            return
            
        print(f"尝试恢复之前添加的键: {previously_selected_keys}")
        
        # 获取新文件中的所有键
        available_keys = set(self.hdf5_model.get_image_keys()) | set(self.hdf5_model.get_data_keys())
        print(f"新文件中可用的键: {available_keys}")
        
        # 找出同时存在于之前添加的键和新文件可用键中的键
        keys_to_restore = previously_selected_keys & available_keys
        keys_to_restore.discard("language")  # language键由load_subtasks方法单独处理
        
        print(f"将要恢复的键: {keys_to_restore}")
        
        # 为每个需要恢复的键添加段
        for key in keys_to_restore:
            # 获取该键基于数据值的连续段
            value_segments = self.hdf5_model.get_value_based_segments(key)
            
            # 添加段到时间轴
            for start, end, value in value_segments:
                self.timeline_widget.add_segment_with_value(key, start, end, value)
            
            # 确保为每个时间轴连接段点击信号
            if key in self.timeline_widget.key_to_timeline:
                timeline = self.timeline_widget.key_to_timeline[key]
                # 断开之前可能的连接，避免重复
                try:
                    timeline.segmentClicked.disconnect(self.on_segment_clicked)
                except:
                    pass
                # 连接段点击信号
                timeline.segmentClicked.connect(self.on_segment_clicked)
                
                # 确保rangeSelected信号也正确连接
                try:
                    timeline.rangeSelected.disconnect()
                except:
                    pass
                # 连接范围选择信号
                current_key = key
                timeline.rangeSelected.connect(
                    lambda start, end, k=current_key: self.on_range_selected(start, end, k)
                )
                
                print(f"已恢复{key}时间轴并连接信号")
            
            # 将键添加回选中的段键集合
            self.selected_segment_keys.add(key)
        
        # 更新按钮状态
        self.remove_segment_btn.setEnabled(len(self.selected_segment_keys) > 0)
    
    def on_file_selected(self, item):
        """当文件列表中的文件被选中时的处理函数"""
        file_path = item.toolTip()
        index = self.file_list_widget.row(item)
        
        # 更新当前文件索引
        self.current_file_index = index
        
        # 加载选中的文件
        self.load_hdf5_file(file_path)
        
        # 更新文件导航按钮状态
        self.update_file_navigation_buttons()
    
    def prev_file(self):
        """加载上一个文件"""
        if self.current_file_index > 0:
            self.current_file_index -= 1
            self.file_list_widget.setCurrentRow(self.current_file_index)
            self.load_hdf5_file(self.hdf5_files[self.current_file_index])
            self.update_file_navigation_buttons()
    
    def next_file(self):
        """加载下一个文件"""
        if self.current_file_index < len(self.hdf5_files) - 1:
            self.current_file_index += 1
            self.file_list_widget.setCurrentRow(self.current_file_index)
            self.load_hdf5_file(self.hdf5_files[self.current_file_index])
            self.update_file_navigation_buttons()
    
    def update_file_navigation_buttons(self):
        """更新文件导航按钮的启用状态"""
        self.prev_file_btn.setEnabled(self.current_file_index > 0)
        self.next_file_btn.setEnabled(self.current_file_index < len(self.hdf5_files) - 1)
    
    def display_all_images(self):
        """显示当前帧的所有图像"""
        if not self.hdf5_model or self.images_grid_layout is None:
            return
        
        # 清除之前的图像
        self.clear_image_grid()
        
        # 获取所有图像键
        image_keys = self.hdf5_model.get_image_keys()
        
        if not image_keys:
            return
        
        # 获取当前帧
        current_frame = self.timeline_widget.get_current_frame()
        
        # 获取滚动区域的可用大小
        scroll_area_size = self.images_scroll_area.size()
        available_width = scroll_area_size.width() - 40  # 减去滚动条和边距
        available_height = scroll_area_size.height() - 40
        
        # 水平并排显示所有图像
        num_images = len(image_keys)
        
        # 计算每个图像的最大尺寸（水平排列）
        max_image_width = max(200, (available_width - num_images * 15) // num_images)  # 最小200像素
        max_image_height = max(150, available_height - 50)  # 减去标题高度
        
        # 为每个图像键创建并显示图像（水平排列）
        for i, key in enumerate(image_keys):
            # 获取图像数据
            image_data = self.hdf5_model.get_image(key, current_frame)
            
            if image_data is not None:
                # 创建图像容器
                image_container = QWidget()
                image_layout = QVBoxLayout(image_container)
                image_layout.setContentsMargins(5, 5, 5, 5)
                image_layout.setSpacing(5)
                
                # 创建图像标题标签
                title_label = QLabel(key)
                title_label.setAlignment(Qt.AlignCenter)
                title_label.setStyleSheet("font-weight: bold; color: #333; font-size: 12px;")
                title_label.setMaximumHeight(25)
                title_label.setMinimumHeight(25)
                
                # 创建图像标签
                image_label = QLabel()
                image_label.setAlignment(Qt.AlignCenter)
                image_label.setMinimumSize(max_image_width, max_image_height)
                image_label.setMaximumSize(max_image_width, max_image_height)
                image_label.setStyleSheet("""
                    QLabel {
                        border: 1px solid #ddd;
                        border-radius: 4px;
                        background-color: white;
                    }
                """)
                
                # 添加到布局
                image_layout.addWidget(title_label)
                image_layout.addWidget(image_label, 1)  # 让图像标签占据剩余空间
                
                # 将图像容器添加到网格（水平排列，都在第0行）
                self.images_grid_layout.addWidget(image_container, 0, i)
                
                # 显示图像
                self.display_image_in_label(image_data, image_label)
        
        # 设置网格布局的拉伸因子，让所有列均匀分布
        for col in range(num_images):
            self.images_grid_layout.setColumnStretch(col, 1)
        
        # 确保只有一行，并让这一行占据所有可用空间
        self.images_grid_layout.setRowStretch(0, 1)
    
    def clear_image_grid(self):
        """清除图像网格中的所有图像"""
        # 检查images_grid_layout是否已初始化
        if self.images_grid_layout is None:
            return
            
        # 移除网格布局中的所有部件
        while self.images_grid_layout.count():
            item = self.images_grid_layout.takeAt(0)
            widget = item.widget()
            if widget:
                widget.deleteLater()
    
    def display_image_in_label(self, image_data, label):
        """在标签中显示图像"""
        if image_data is None:
            label.clear()
            label.setText("无图像数据")
            label.setStyleSheet(label.styleSheet() + "color: #999;")
            return
            
        # 将numpy数组转换为QImage
        height, width, channels = image_data.shape
        bytes_per_line = channels * width
        
        if channels == 3:
            format = QImage.Format_RGB888
        elif channels == 4:
            format = QImage.Format_RGBA8888
        else:
            raise ValueError(f"不支持的通道数: {channels}")
        
        # 确保图像数据连续
        if not image_data.flags['C_CONTIGUOUS']:
            image_data = np.ascontiguousarray(image_data)
            
        # 创建QImage和QPixmap
        q_image = QImage(image_data.data, width, height, bytes_per_line, format)
        pixmap = QPixmap.fromImage(q_image)
        
        # 获取标签的实际可用大小（减去边距和边框）
        label_size = label.size()
        available_width = max(50, label_size.width() - 10)  # 减去边距
        available_height = max(50, label_size.height() - 10)
        
        # 缩放图像以适应标签大小，保持纵横比
        scaled_pixmap = pixmap.scaled(
            available_width, 
            available_height,
            Qt.KeepAspectRatio, 
            Qt.SmoothTransformation
        )
        
        # 设置图像标签
        label.setPixmap(scaled_pixmap)
        label.setText("")  # 清除文本
        label.setStyleSheet(label.styleSheet().replace("color: #999;", ""))  # 移除文本颜色
    
    def update_ui_with_model(self):
        """使用模型数据更新UI"""
        if not self.hdf5_model:
            return
            
        # 更新图像列表
        self.image_list_widget.clear()
        for key in self.hdf5_model.get_image_keys():
            self.image_list_widget.addItem(key)
            
        # 更新数据列表
        self.data_list_widget.clear()
        for key in self.hdf5_model.get_data_keys():
            self.data_list_widget.addItem(key)
        
        # 更新时间轴帧数
        frame_count = self.hdf5_model.get_frame_count()
        self.timeline_widget.set_frame_count(frame_count)
        
        # 更新编辑键选择器
        self.update_edit_key_combo()
        
        # 更新UI后，不调用reset_segments，而是在restore_selected_keys和load_current_key_data中处理
        # 保留非当前编辑键的段
        non_current_keys = list(self.selected_segment_keys - {self.current_editing_key})
        print(f"更新UI，保留的非当前编辑键段: {non_current_keys}")
        
        # 重置当前编辑键段并加载新的数据
        # 注意：这会移除旧的当前编辑键段，但保留其他key的段
        if self.current_editing_key in self.timeline_widget.key_to_timeline:
            self.timeline_widget.key_to_timeline[self.current_editing_key].clear_segments()
        
        # 加载当前编辑键的数据
        self.load_current_key_data()
        
        # 启用控制按钮
        self.add_key_btn.setEnabled(True)
    
    def on_frame_changed(self, frame: int):
        """
        当时间轴上的当前帧改变时的处理函数
        
        Args:
            frame: 新的帧索引
        """
        # 更新所有图像窗口
        for key, window in self.image_windows.items():
            if window.isVisible():
                image_data = self.hdf5_model.get_image(key, frame)
                window.set_image(image_data)
        
        # 更新状态栏
        self.statusBar().showMessage(f"当前帧: {frame}/{self.hdf5_model.get_frame_count() - 1}")
        
        # 更新language显示
        self.update_language_display(frame)
        
        # 更新数据显示（如果有选中的数据键）
        selected_data_items = self.data_list_widget.selectedItems()
        if selected_data_items:
            # 更新第一个选中项的数据显示
            first_item = selected_data_items[0]
            key = first_item.text()
            self.update_data_display(key)
        
        # 更新图像网格中的所有图像
        self.display_all_images()
    
    def keyPressEvent(self, event: QKeyEvent):
        """处理键盘事件"""
        # 使用左右方向键导航文件
        if event.key() == Qt.Key_Left:
            self.prev_file()
        elif event.key() == Qt.Key_Right:
            self.next_file()
        # 使用Enter键进入范围选择模式
        elif event.key() == Qt.Key_Return or event.key() == Qt.Key_Enter:
            # 如果时间轴已初始化，则转发事件到时间轴
            if hasattr(self, 'timeline_widget') and self.timeline_widget:
                print("检测到Enter键，转发到时间轴进入范围选择模式")
                self.timeline_widget.keyPressEvent(event)
        # 使用Ctrl+N新增键
        elif event.key() == Qt.Key_N and event.modifiers() == Qt.ControlModifier:
            if self.hdf5_model:
                self.add_new_key()

        # 使用Tab键切换编辑键
        elif event.key() == Qt.Key_Tab:
            if self.edit_key_combo.count() > 0:
                current_index = self.edit_key_combo.currentIndex()
                next_index = (current_index + 1) % self.edit_key_combo.count()
                self.edit_key_combo.setCurrentIndex(next_index)
        # 使用Shift+Tab键反向切换编辑键
        elif event.key() == Qt.Key_Tab and event.modifiers() == Qt.ShiftModifier:
            if self.edit_key_combo.count() > 0:
                current_index = self.edit_key_combo.currentIndex()
                prev_index = (current_index - 1) % self.edit_key_combo.count()
                self.edit_key_combo.setCurrentIndex(prev_index)
        else:
            super().keyPressEvent(event)
    
    def load_subtasks(self):
        """加载已有的languages（兼容旧接口）"""
        self.load_current_key_data()
    
    def on_image_selection_changed(self):
        """处理图像选择变化"""
        selected_items = self.image_list_widget.selectedItems()
        
        for item in selected_items:
            key = item.text()
            
            # 如果该键的窗口尚未创建，则创建它
            if key not in self.image_windows:
                window = ImageWindow(key)
                self.image_windows[key] = window
                window.show()
            else:
                # 如果窗口已存在但被关闭，则重新显示
                window = self.image_windows[key]
                if not window.isVisible():
                    window.show()
            
            # 更新图像窗口的内容
            self.update_image_window(key)
    
    def update_image_window(self, key: str):
        """更新图像窗口的内容"""
        if not self.hdf5_model or key not in self.image_windows:
            return
        
        # 获取当前帧的图像数据
        image_data = self.hdf5_model.get_image(key, self.timeline_widget.current_frame)
        
        # 更新图像窗口
        window = self.image_windows[key]
        window.set_image(image_data)
    
    def on_data_selection_changed(self):
        """处理数据选择变化"""
        selected_items = self.data_list_widget.selectedItems()
        
        if not selected_items:
            self.data_value_label.setText("请选择数据键和帧...")
            self.add_segment_btn.setEnabled(False)
            self.remove_segment_btn.setEnabled(False)
            return
        
        # 启用"添加到进度条"按钮
        self.add_segment_btn.setEnabled(True)
        
        # 检查是否有选中的键已在进度条中，如果有则启用"移除"按钮
        has_selected_key_in_timeline = False
        for item in selected_items:
            key = item.text()
            if key in self.selected_segment_keys:
                has_selected_key_in_timeline = True
                break
        
        self.remove_segment_btn.setEnabled(has_selected_key_in_timeline)
        
        # 只显示第一个选中项的数据
        first_item = selected_items[0]
        key = first_item.text()
        
        self.update_data_display(key)
    
    def update_data_display(self, key: str):
        """更新数据显示，添加安全错误处理"""
        if not self.hdf5_model:
            return
        
        current_frame = self.timeline_widget.current_frame
        
        try:
            # 获取数据
            data = self.hdf5_model.get_data(key, current_frame)
            
            if data is None:
                self.data_value_label.setText(f"{key}: 无数据")
                return
            
            # 显示数据
            if isinstance(data, np.ndarray):
                if data.size > 100:  # 数据太大，只显示形状
                    self.data_value_label.setText(f"{key}: 形状={data.shape}, 类型={data.dtype}")
                else:
                    # 处理可能的字节字符串
                    text = str(data)
                    # 限制显示长度，避免界面卡顿
                    if len(text) > 200:
                        text = text[:200] + "..."
                    self.data_value_label.setText(f"{key}: {text}")
            else:
                # 处理单个字节字符串或其他类型
                if isinstance(data, bytes):
                    try:
                        text = data.decode('utf-8', errors='replace')
                    except:
                        text = str(data)
                else:
                    text = str(data)
                
                # 限制显示长度
                if len(text) > 200:
                    text = text[:200] + "..."
                self.data_value_label.setText(f"{key}: {text}")
                
        except Exception as e:
            # 捕获所有异常，防止软件崩溃
            error_msg = f"{key}: 数据读取错误 - {str(e)[:100]}"
            self.data_value_label.setText(error_msg)
            print(f"更新数据显示时出错: {key}, 错误: {e}")
            
            # 尝试获取数据集基本信息
            try:
                info = self.hdf5_model.get_data_info(key)
                if info:
                    self.data_value_label.setText(f"{key}: 形状={info.get('shape', '未知')}, 类型={info.get('dtype', '未知')}")
            except:
                pass
    
    def on_segment_clicked(self, segment: TimelineSegment):
        """处理段点击事件"""
        if not self.hdf5_model:
            return
        
        print(f"\n处理段点击事件: {segment.key} {segment.start}-{segment.end}")
        print(f"当前编辑键: {self.current_editing_key}")
        
        # 允许重新设置任何段的Language，不跳过已完成的段
        is_resetting = segment.completed
        existing_description = segment.subtask if is_resetting else ""
        
        # 弹出Language输入对话框
        dialog = EnhancedInputDialog(self, segment.start, segment.end, self.current_editing_key, existing_description if is_resetting else "")
        
        if dialog.exec_() == QDialog.Accepted:
            description = dialog.get_description()
            
            if description:
                print(f"设置段 {segment.start}-{segment.end} 的{self.current_editing_key}描述为: {description}")
                # 始终设置到当前编辑键，而不是段所属的键
                success = self.hdf5_model.set_language_for_key(self.current_editing_key, segment.start, segment.end, description)
                
                if success:
                    # 强制清除当前键的缓存，确保获取最新数据
                    if self.current_editing_key in self.hdf5_model.languages:
                        del self.hdf5_model.languages[self.current_editing_key]
                    
                    # 如果点击的段属于当前编辑键，更新该段的显示
                    if segment.key == self.current_editing_key:
                        self.timeline_widget.set_segment_completed(segment, True, description)
                    
                    # 只重新加载当前编辑键的数据，不影响其他键的时间轴显示
                    self.load_current_key_data_without_hiding_others()
                    
                    # 确保当前帧的language显示也更新
                    current_frame = self.timeline_widget.current_frame
                    if segment.start <= current_frame <= segment.end:
                        print(f"当前帧 {current_frame} 在新设置的段内，立即更新显示")
                        self.update_language_display(current_frame)
                    
                    # 强制重绘界面
                    self.repaint()
                else:
                    QMessageBox.warning(self, "警告", f"设置{self.current_editing_key}失败")
            else:
                print("用户输入的描述为空")
    
    def remove_selected_keys_from_timeline(self):
        """从时间轴移除所选键"""
        if not self.hdf5_model:
            return
        
        # 获取所选数据键
        selected_items = self.data_list_widget.selectedItems()
        if not selected_items:
            return
        
        # 移除每个选中的键
        for item in selected_items:
            key = item.text()
            
            # 检查是否已经添加了该键
            if key in self.selected_segment_keys:
                # 从时间轴移除段
                self.timeline_widget.remove_segments_by_key(key)
                self.selected_segment_keys.remove(key)
        
        # 禁用移除按钮（如果没有选中的键在时间轴上）
        if not any(key in self.selected_segment_keys for key in [item.text() for item in selected_items]):
            self.remove_segment_btn.setEnabled(False)
    
    def on_range_selected(self, start: int, end: int, key: str):
        """
        处理范围选择事件
        
        Args:
            start: 起始帧
            end: 结束帧
            key: 关联的键（这个参数现在主要用于标识来源，实际设置时使用当前编辑键）
        """
        if not self.hdf5_model:
            return
        
        print(f"\n处理范围选择事件: 来源键={key} {start}-{end}")
        print(f"当前编辑键: {self.current_editing_key}")
        
        # 禁用范围选择模式
        self.timeline_widget.toggle_range_selection(False)
        
        # 弹出Language输入对话框
        dialog = EnhancedInputDialog(self, start, end, self.current_editing_key)
        
        if dialog.exec_() == QDialog.Accepted:
            description = dialog.get_description()
            
            if description:
                print(f"设置范围 {start}-{end} 的{self.current_editing_key}描述为: {description}")
                # 始终设置到当前编辑键，而不是来源键
                success = self.hdf5_model.set_language_for_key(self.current_editing_key, start, end, description)
                
                if success:
                    # 强制清除当前键的缓存，确保获取最新数据
                    if self.current_editing_key in self.hdf5_model.languages:
                        del self.hdf5_model.languages[self.current_editing_key]
                    
                    # 重新加载当前编辑键数据以更新所有显示
                    self.load_current_key_data_without_hiding_others()
                    
                    # 确保当前帧的language显示也更新
                    current_frame = self.timeline_widget.current_frame
                    if start <= current_frame <= end:
                        print(f"当前帧 {current_frame} 在新设置的范围内，立即更新显示")
                        self.update_language_display(current_frame)
                    
                    # 强制重绘界面
                    self.repaint()
                else:
                    QMessageBox.warning(self, "警告", f"设置{self.current_editing_key}失败")
            else:
                print("用户输入的描述为空")
    
    def update_language_display(self, frame: int):
        """更新当前帧的language显示"""
        if not self.hdf5_model:
            print("警告: hdf5_model 为 None，无法更新 language 显示")
            return
        
        # 直接打印出当前帧和所有信息，帮助调试
        print(f"\n==== 更新帧 {frame} 的{self.current_editing_key}显示 ====")
        
        # 定义变量跟踪是否找到了language信息
        found_language = False
        language_text = "无"
        
        # 首先尝试从timeline段获取language信息
        if self.current_editing_key in self.timeline_widget.key_to_timeline:
            timeline = self.timeline_widget.key_to_timeline[self.current_editing_key]
            print(f"找到{self.current_editing_key}时间轴，共有 {len(timeline.segments)} 个段")
            
            # 直接打印所有段的信息
            for i, segment in enumerate(timeline.segments):
                print(f"段 {i+1}: 帧 {segment.start}-{segment.end}，完成状态: {segment.completed}，描述: '{segment.subtask}'")
                # 检查当前帧是否在该段内
                if segment.start <= frame <= segment.end:
                    print(f"-> 当前帧 {frame} 在该段内!")
                    if segment.completed and segment.subtask:
                        language_text = segment.subtask
                        found_language = True
                        print(f"-> 使用该段的描述: '{language_text}'")
                        # 找到后立即中断，不再继续查找
                        break
        else:
            print(f"未找到{self.current_editing_key}时间轴")
        
        # 如果从timeline段没有找到，再尝试从HDF5文件直接读取
        if not found_language:
            print(f"尝试从HDF5文件直接读取{self.current_editing_key}数据...")
            if self.current_editing_key in self.hdf5_model.file:
                try:
                    language_data = self.hdf5_model.file[self.current_editing_key]
                    print(f"{self.current_editing_key}数据集形状: {language_data.shape}, 类型: {language_data.dtype}")
                    
                    if frame < language_data.shape[0]:
                        value = language_data[frame, 0] if len(language_data.shape) > 1 else language_data[frame]
                        print(f"帧 {frame} 的原始值: {value}, 类型: {type(value)}")
                        
                        # 处理各种类型的值
                        if isinstance(value, bytes):
                            try:
                                text = value.decode('utf-8', errors='replace')
                            except Exception as e:
                                print(f"解码bytes失败: {e}, 尝试直接使用str")
                                text = str(value)
                        else:
                            text = str(value)
                        
                        print(f"解码后的值: '{text}'")
                        
                        # 过滤无效值
                        if text and text.strip() and text != "0" and text != "b'0'" and text != "b''":
                            language_text = text.strip()
                            found_language = True
                            print(f"从HDF5文件找到有效语言描述: '{language_text}'")
                    else:
                        print(f"帧索引 {frame} 超出了{self.current_editing_key}数据集范围 {language_data.shape[0]}")
                except Exception as e:
                    print(f"读取{self.current_editing_key}数据时出错: {e}")
            else:
                print(f"HDF5文件中没有{self.current_editing_key}数据集")
        
        # 从language缓存中检查
        if not found_language:
            print(f"尝试从HDF5模型的{self.current_editing_key}缓存中查找...")
            languages = self.hdf5_model.get_languages_for_key(self.current_editing_key)
            print(f"缓存中有 {len(languages)} 个{self.current_editing_key}段")
            
            for (start, end), desc in languages.items():
                print(f"缓存段: {start}-{end}, 描述: '{desc}'")
                if start <= frame <= end:
                    print(f"-> 当前帧 {frame} 在该缓存段内!")
                    language_text = desc
                    found_language = True
                    print(f"-> 使用缓存的描述: '{language_text}'")
                    break
        
        # 最终更新UI显示
        print(f"最终要显示的{self.current_editing_key}文本: '{language_text}'")
        
        # 确保language_text是一个字符串，并处理可能的前缀 b'...'
        if language_text.startswith("b'") and language_text.endswith("'"):
            try:
                # 尝试去除 b'...' 格式
                inner_text = language_text[2:-1]
                language_text = inner_text
            except:
                pass
        
        if language_text == "无":
            print(f"警告: {self.current_editing_key}_text 仍为'无'，未找到有效的 {self.current_editing_key} 信息")
            print("检查前面的调试输出以确定问题所在")
        
        # 设置文本内容
        self.language_value_label.setText(language_text)
        
        # 设置样式
        if found_language:
            # 设置随机背景色（基于文本），但保持柔和
            hash_val = hash(language_text)
            r = 240 - (hash_val & 0x3F)  # 较浅的红色值 (192-240)
            g = 255 - (hash_val & 0x1F)  # 较浅的绿色值 (224-255)
            b = 250 - ((hash_val >> 6) & 0x1F)  # 较浅的蓝色值 (224-250)
            style = f"""
                background-color: rgb({r}, {g}, {b});
                border: 1px solid #d0e0f0;
                border-radius: 4px;
                padding: 8px;
                font-weight: bold;
                min-height: 20px;
            """
        else:
            # 无language数据时的样式
            style = """
                background-color: #f0f8ff;
                border: 1px solid #d0e0f0;
                border-radius: 4px;
                padding: 8px;
                font-weight: bold;
                min-height: 20px;
            """
        
        self.language_value_label.setStyleSheet(style)
        print(f"==== {self.current_editing_key}显示更新完成 ====\n")
    
    def reload_current_key_display(self):
        """重新加载当前编辑键显示"""
        if not self.hdf5_model:
            return
            
        print(f"重新加载{self.current_editing_key}显示")
            
        # 隐藏所有其他键的时间轴显示，只保留当前编辑键
        for key, timeline in self.timeline_widget.key_to_timeline.items():
            if key != self.current_editing_key:
                # 隐藏时间轴
                timeline.hide()
                # 查找并隐藏对应的标签
                label_text = f"{key}标签轨道:"
                for i in range(self.timeline_widget.timelines_layout.count()):
                    item = self.timeline_widget.timelines_layout.itemAt(i)
                    if item and item.widget():
                        widget = item.widget()
                        if isinstance(widget, QLabel) and label_text in widget.text():
                            widget.hide()
                            break
                print(f"隐藏了 {key} 的时间轴和标签")
        
        # 确保当前键的时间轴存在并显示
        print(f"创建/显示 {self.current_editing_key} 时间轴")
        timeline = self.timeline_widget.add_timeline_for_key(self.current_editing_key)
        
        # 显示时间轴（如果之前被隐藏）
        timeline.show()
        
        # 确保时间轴能够接收焦点和键盘事件
        timeline.setFocusPolicy(Qt.StrongFocus)
        timeline.setEnabled(True)
        
        # 显示对应的标签
        label_text = f"{self.current_editing_key}标签轨道:"
        for i in range(self.timeline_widget.timelines_layout.count()):
            item = self.timeline_widget.timelines_layout.itemAt(i)
            if item and item.widget():
                widget = item.widget()
                if isinstance(widget, QLabel) and label_text in widget.text():
                    widget.show()
                    break
        
        # 连接segment点击信号
        try:
            timeline.segmentClicked.disconnect(self.on_segment_clicked)
        except:
            pass
        timeline.segmentClicked.connect(self.on_segment_clicked)
        print(f"已连接 {self.current_editing_key} 时间轴的segmentClicked信号")
        
        # 连接范围选择信号
        try:
            timeline.rangeSelected.disconnect()
        except:
            pass
        timeline.rangeSelected.connect(lambda start, end, key=self.current_editing_key: self.on_range_selected(start, end, key))
        print(f"已连接 {self.current_editing_key} 时间轴的rangeSelected信号")
        
        # 清除现有段
        timeline.clear_segments()
        
        # 获取当前键的所有language数据
        languages = self.hdf5_model.get_languages_for_key(self.current_editing_key)
        print(f"从HDF5模型获取到 {len(languages)} 个 {self.current_editing_key} 段")
        
        # 将当前键添加到选中的段键集合中，以便显示在时间轴上
        self.selected_segment_keys.add(self.current_editing_key)
        
        # 添加language段
        if languages:
            for (start, end), desc in languages.items():
                print(f"添加 {self.current_editing_key} 段 {start}-{end}: '{desc}'")
                segment = self.timeline_widget.add_segment(self.current_editing_key, start, end)
                self.timeline_widget.set_segment_completed(segment, True, desc)
            
            # 找到当前帧所在的language段并立即更新显示
            current_frame = self.timeline_widget.current_frame
            for (start, end), desc in languages.items():
                if start <= current_frame <= end:
                    print(f"当前帧 {current_frame} 在 {self.current_editing_key} 段 {start}-{end} 内，立即更新显示")
                    self.update_language_display(current_frame)
                    break
            else:
                # 如果当前帧不在任何段内，显示"无"
                self.language_value_label.setText("无")
        else:
            print(f"没有找到 {self.current_editing_key} 数据")
            self.language_value_label.setText("无")
        
        print(f"----- {self.current_editing_key} 数据加载完成 -----\n")
    
    def resizeEvent(self, event):
        """处理窗口大小变化事件"""
        super().resizeEvent(event)
        
        # 启动延迟更新定时器，避免频繁重绘
        if hasattr(self, 'resize_timer'):
            self.resize_timer.stop()
            self.resize_timer.start(300)  # 300ms延迟
    
    def on_resize_finished(self):
        """窗口大小变化完成后的处理"""
        # 如果有HDF5模型且图像显示区域已初始化，重新显示图像
        if self.hdf5_model and self.images_grid_layout is not None:
            self.display_all_images()
    
    def reload_language_display(self):
        """重新加载language显示（兼容旧接口）"""
        self.reload_current_key_display()
    
    def closeEvent(self, event):
        """处理窗口关闭事件"""
        # 停止播放
        if hasattr(self.timeline_widget, 'play_timer') and self.timeline_widget.play_timer.isActive():
            self.timeline_widget.play_timer.stop()
        
        # 关闭所有图像窗口
        for window in self.image_windows.values():
            window.close()
        
        # 关闭HDF5模型
        if self.hdf5_model:
            self.hdf5_model.close()
        
        # 接受关闭事件
        event.accept()
    
    def on_segments_multi_selected(self, segments, key):
        """
        处理多段选择事件
        
        Args:
            segments: 选中的段列表
            key: 关联的键（这个参数现在主要用于标识来源，实际设置时使用当前编辑键）
        """
        if not self.hdf5_model:
            return
        
        print(f"处理多段选择事件，来源键: {key}，选中 {len(segments)} 个段")
        print(f"当前编辑键: {self.current_editing_key}")
        
        # 如果没有选中的段，则不处理
        if not segments:
            print("没有选中的段，不处理")
            return
        
        # 找出最小起始帧和最大结束帧
        start_frame = min(segment.start for segment in segments)
        end_frame = max(segment.end for segment in segments)
        
        # 弹出Language输入对话框
        dialog = SubtaskInputDialog(self, start_frame, end_frame)
        dialog.setWindowTitle(f"为帧{start_frame}至{end_frame}设置{self.current_editing_key}描述 - 共 {len(segments)} 个段")
        
        # 无论用户是否接受对话框，都应该清除选择状态
        result = dialog.exec_()
        
        # 首先获取对应的timeline，准备清除选择
        timeline = None
        for t in self.timeline_widget.timelines:
            if t.key == key:
                timeline = t
                break
        
        if result == QDialog.Accepted:
            description = dialog.get_description()
            
            if description:
                print(f"为 {len(segments)} 个段设置{self.current_editing_key}描述: {description}")
                
                # 始终设置到当前编辑键，而不是来源键
                success = self.hdf5_model.set_language_for_key(self.current_editing_key, start_frame, end_frame, description)
                
                if success:
                    # 重新加载当前编辑键数据以更新所有显示
                    self.load_current_key_data_without_hiding_others()
                    
                    # 确保当前帧的language显示也更新
                    current_frame = self.timeline_widget.current_frame
                    self.update_language_display(current_frame)
                    
                    # 提示成功消息
                    QMessageBox.information(self, "成功", f"已为 {len(segments)} 个段设置{self.current_editing_key}")
                else:
                    QMessageBox.warning(self, "警告", f"设置{self.current_editing_key}失败")
            else:
                print("用户取消了输入")
        else:
            print("用户取消了输入")
        
        # 无论用户是否接受，都清除选择
        if timeline:
            timeline.selected_segments.clear()
            timeline.update()
            print(f"清除了 {key} 时间轴上的所有选择")
    
    def next_frame(self):
        """播放时前进到下一帧"""
        if not self.hdf5_model:
            return
            
        current_frame = self.timeline_widget.get_current_frame()
        frame_count = self.hdf5_model.get_frame_count()
        
        if current_frame < frame_count - 1:
            next_frame = current_frame + 1
        else:
            next_frame = 0  # 循环播放
        
        self.timeline_widget.set_current_frame(next_frame)
    
    def toggle_play(self):
        """切换播放/暂停状态"""
        if self.play_timer.isActive():
            # 当前正在播放，停止播放
            self.play_timer.stop()
            self.play_btn.setText("播放")
        else:
            # 当前已停止，开始播放
            self.play_timer.start(100)  # 每100毫秒更新一次，约10帧/秒
            self.play_btn.setText("暂停")
    
    def add_selected_keys_to_timeline(self):
        """将所选键添加到时间轴"""
        if not self.hdf5_model:
            return
        
        # 获取所选数据键
        selected_items = self.data_list_widget.selectedItems()
        if not selected_items:
            return
        
        # 为每个选中的键添加段
        for item in selected_items:
            key = item.text()
            
            # 检查是否已经添加了该键
            if key in self.selected_segment_keys:
                # 先移除现有的段
                self.timeline_widget.remove_segments_by_key(key)
                self.selected_segment_keys.remove(key)
            
            # 获取该键基于数据值的连续段
            value_segments = self.hdf5_model.get_value_based_segments(key)
            
            # 添加段到时间轴
            for start, end, value in value_segments:
                segment = self.timeline_widget.add_segment_with_value(key, start, end, value)
            
            # 检查是否成功创建了时间轴，并连接信号
            if key in self.timeline_widget.key_to_timeline:
                timeline = self.timeline_widget.key_to_timeline[key]
                
                # 断开之前可能的连接，避免重复
                try:
                    timeline.segmentClicked.disconnect(self.on_segment_clicked)
                except:
                    pass
                    
                # 连接段点击信号
                timeline.segmentClicked.connect(self.on_segment_clicked)
                print(f"已为{key}时间轴连接segmentClicked信号")
                
                # 确保rangeSelected信号也正确连接
                try:
                    # 使用lambda来传递timeline.key参数
                    timeline.rangeSelected.disconnect()
                except:
                    pass
                    
                # 连接范围选择信号，确保传递正确的key参数
                # 注意：需要使用lambda捕获当前的key值
                current_key = key  # 捕获当前迭代的key
                timeline.rangeSelected.connect(
                    lambda start, end, k=current_key: self.on_range_selected(start, end, k)
                )
                print(f"已为{key}时间轴连接rangeSelected信号")
            
            # 记录已添加的键
            self.selected_segment_keys.add(key)
        
        # 启用移除按钮
        self.remove_segment_btn.setEnabled(True)

    def on_edit_key_changed(self, text):
        """处理编辑键变化"""
        if not text:
            return
            
        old_key = self.current_editing_key
        self.current_editing_key = text
        print(f"切换编辑键: {old_key} -> {self.current_editing_key}")
        
        # 更新界面标题
        self.language_title.setText(f"当前{self.current_editing_key}:")
        
        # 更新UI状态
        self.add_key_btn.setEnabled(bool(self.hdf5_model))
        
        # 如果有HDF5模型，进行数据切换
        if self.hdf5_model:
            # 隐藏旧的编辑键时间轴（如果不同的话）
            if old_key and old_key != self.current_editing_key and old_key in self.timeline_widget.key_to_timeline:
                old_timeline = self.timeline_widget.key_to_timeline[old_key]
                # 不删除，只是从选中的段键集合中移除
                if old_key in self.selected_segment_keys:
                    self.selected_segment_keys.remove(old_key)
                print(f"从选中段键中移除旧键: {old_key}")
            
            # 加载当前编辑键的数据
            self.load_current_key_data()
            
            # 更新当前帧的显示
            current_frame = self.timeline_widget.current_frame
            self.update_language_display(current_frame)
    
    def add_new_key(self):
        """添加新的language键"""
        if not self.hdf5_model:
            QMessageBox.warning(self, "警告", "请先打开HDF5文件")
            return
            
        key, ok = QInputDialog.getText(self, "新增Language键", 
                                      "请输入新的Language键名称:", 
                                      text="language_new")
        if ok and key:
            key = key.strip()
            if not key:
                QMessageBox.warning(self, "警告", "键名称不能为空")
                return
                
            # 检查键是否已存在
            if key in self.hdf5_model.file:
                QMessageBox.warning(self, "警告", f"键 '{key}' 已存在")
                return
            
            try:
                # 在HDF5文件中创建新的language键
                success = self.hdf5_model.create_language_key(key)
                if success:
                    # 更新编辑键选择器
                    self.update_edit_key_combo()
                    
                    # 立即设置为当前编辑键
                    index = self.edit_key_combo.findText(key)
                    if index >= 0:
                        self.edit_key_combo.setCurrentIndex(index)
                        # 手动触发切换，确保界面更新
                        self.on_edit_key_changed(key)
                    
                    QMessageBox.information(self, "成功", f"成功创建Language键 '{key}' 并已切换到该键")
                else:
                    QMessageBox.warning(self, "错误", f"创建Language键 '{key}' 失败")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"创建Language键时出错: {e}")
    

    def update_edit_key_combo(self):
        """更新编辑键选择器"""
        print("=== 开始更新编辑键选择器 ===")
        
        if not self.hdf5_model:
            self.edit_key_combo.clear()
            self.current_editing_key = "language"
            # 清空时也要更新界面标题
            self.language_title.setText(f"当前{self.current_editing_key}:")
            print("HDF5模型为空，清空下拉框")
            return
            
        # 获取所有可能的language键
        language_keys = self.hdf5_model.get_language_keys()
        print(f"获取到的language键: {language_keys}")
        
        # 保存当前选择
        current_text = self.edit_key_combo.currentText()
        print(f"当前下拉框选中的文本: '{current_text}'")
        
        # 临时阻断信号，避免触发不必要的变化事件
        self.edit_key_combo.blockSignals(True)
        
        try:
            # 更新下拉框
            self.edit_key_combo.clear()
            for key in language_keys:
                self.edit_key_combo.addItem(key)
                print(f"添加选项: '{key}'")
            
            # 恢复选择或设置默认值
            if current_text and current_text in language_keys:
                index = self.edit_key_combo.findText(current_text)
                if index >= 0:
                    self.edit_key_combo.setCurrentIndex(index)
                    self.current_editing_key = current_text
                    print(f"恢复选择: '{current_text}' (索引: {index})")
                else:
                    print(f"未找到文本 '{current_text}' 对应的索引")
            elif language_keys:
                # 优先选择"language"键，如果不存在则选择第一个
                if "language" in language_keys:
                    index = self.edit_key_combo.findText("language")
                    self.edit_key_combo.setCurrentIndex(index)
                    self.current_editing_key = "language"
                    print("设置默认选择: 'language'")
                else:
                    self.edit_key_combo.setCurrentIndex(0)
                    self.current_editing_key = language_keys[0]
                    print(f"设置第一个选项: '{language_keys[0]}'")
            else:
                self.current_editing_key = "language"
                print("没有可用键，设置默认值: 'language'")
        
        finally:
            # 确保信号总是能够恢复
            self.edit_key_combo.blockSignals(False)
            print("恢复信号连接")
        
        # 手动更新界面标题和其他相关显示
        self.language_title.setText(f"当前{self.current_editing_key}:")
        print(f"更新界面标题: '当前{self.current_editing_key}:'")
        
        # 更新UI状态
        self.add_key_btn.setEnabled(bool(self.hdf5_model))
        
        # 检查最终状态
        final_text = self.edit_key_combo.currentText()
        final_index = self.edit_key_combo.currentIndex()
        print(f"最终状态: 当前文本='{final_text}', 当前索引={final_index}, 总项数={self.edit_key_combo.count()}")
        print("=== 编辑键选择器更新完成 ===\n")
    
    def load_current_key_data(self):
        """加载当前编辑键的数据"""
        if not self.hdf5_model or not self.current_editing_key:
            return
        
        print(f"\n----- 开始加载 {self.current_editing_key} 数据 -----")
        
        # 隐藏所有其他键的时间轴显示，只保留当前编辑键
        for key, timeline in self.timeline_widget.key_to_timeline.items():
            if key != self.current_editing_key:
                # 隐藏时间轴
                timeline.hide()
                # 查找并隐藏对应的标签
                label_text = f"{key}标签轨道:"
                for i in range(self.timeline_widget.timelines_layout.count()):
                    item = self.timeline_widget.timelines_layout.itemAt(i)
                    if item and item.widget():
                        widget = item.widget()
                        if isinstance(widget, QLabel) and label_text in widget.text():
                            widget.hide()
                            break
                print(f"隐藏了 {key} 的时间轴和标签")
        
        # 确保当前键的时间轴存在并显示
        print(f"创建/显示 {self.current_editing_key} 时间轴")
        timeline = self.timeline_widget.add_timeline_for_key(self.current_editing_key)
        
        # 显示时间轴（如果之前被隐藏）
        timeline.show()
        
        # 确保时间轴能够接收焦点和键盘事件
        timeline.setFocusPolicy(Qt.StrongFocus)
        timeline.setEnabled(True)
        
        # 显示对应的标签
        label_text = f"{self.current_editing_key}标签轨道:"
        for i in range(self.timeline_widget.timelines_layout.count()):
            item = self.timeline_widget.timelines_layout.itemAt(i)
            if item and item.widget():
                widget = item.widget()
                if isinstance(widget, QLabel) and label_text in widget.text():
                    widget.show()
                    break
        
        # 连接segment点击信号
        try:
            timeline.segmentClicked.disconnect(self.on_segment_clicked)
        except:
            pass
        timeline.segmentClicked.connect(self.on_segment_clicked)
        print(f"已连接 {self.current_editing_key} 时间轴的segmentClicked信号")
        
        # 连接范围选择信号
        try:
            timeline.rangeSelected.disconnect()
        except:
            pass
        timeline.rangeSelected.connect(lambda start, end, key=self.current_editing_key: self.on_range_selected(start, end, key))
        print(f"已连接 {self.current_editing_key} 时间轴的rangeSelected信号")
        
        # 清除现有段
        timeline.clear_segments()
        
        # 获取当前键的所有language数据
        languages = self.hdf5_model.get_languages_for_key(self.current_editing_key)
        print(f"从HDF5模型获取到 {len(languages)} 个 {self.current_editing_key} 段")
        
        # 将当前键添加到选中的段键集合中，以便显示在时间轴上
        self.selected_segment_keys.add(self.current_editing_key)
        
        # 添加language段
        if languages:
            for (start, end), desc in languages.items():
                print(f"添加 {self.current_editing_key} 段 {start}-{end}: '{desc}'")
                segment = self.timeline_widget.add_segment(self.current_editing_key, start, end)
                self.timeline_widget.set_segment_completed(segment, True, desc)
            
            # 找到当前帧所在的language段并立即更新显示
            current_frame = self.timeline_widget.current_frame
            for (start, end), desc in languages.items():
                if start <= current_frame <= end:
                    print(f"当前帧 {current_frame} 在 {self.current_editing_key} 段 {start}-{end} 内，立即更新显示")
                    self.update_language_display(current_frame)
                    break
            else:
                # 如果当前帧不在任何段内，显示"无"
                self.language_value_label.setText("无")
        else:
            print(f"没有找到 {self.current_editing_key} 数据")
            self.language_value_label.setText("无")
        
        print(f"----- {self.current_editing_key} 数据加载完成 -----\n")

    def load_current_key_data_without_hiding_others(self):
        """重新加载当前编辑键的数据，不影响其他键的时间轴显示"""
        if not self.hdf5_model or not self.current_editing_key:
            return
        
        print(f"\n----- 开始加载 {self.current_editing_key} 数据 -----")
        
        # 确保当前键的时间轴存在并显示
        print(f"创建/显示 {self.current_editing_key} 时间轴")
        timeline = self.timeline_widget.add_timeline_for_key(self.current_editing_key)
        
        # 显示时间轴（如果之前被隐藏）
        timeline.show()
        
        # 确保时间轴能够接收焦点和键盘事件
        timeline.setFocusPolicy(Qt.StrongFocus)
        timeline.setEnabled(True)
        
        # 连接segment点击信号
        try:
            timeline.segmentClicked.disconnect(self.on_segment_clicked)
        except:
            pass
        timeline.segmentClicked.connect(self.on_segment_clicked)
        print(f"已连接 {self.current_editing_key} 时间轴的segmentClicked信号")
        
        # 连接范围选择信号
        try:
            timeline.rangeSelected.disconnect()
        except:
            pass
        timeline.rangeSelected.connect(lambda start, end, key=self.current_editing_key: self.on_range_selected(start, end, key))
        print(f"已连接 {self.current_editing_key} 时间轴的rangeSelected信号")
        
        # 清除现有段
        timeline.clear_segments()
        
        # 获取当前键的所有language数据
        languages = self.hdf5_model.get_languages_for_key(self.current_editing_key)
        print(f"从HDF5模型获取到 {len(languages)} 个 {self.current_editing_key} 段")
        
        # 将当前键添加到选中的段键集合中，以便显示在时间轴上
        self.selected_segment_keys.add(self.current_editing_key)
        
        # 添加language段
        if languages:
            for (start, end), desc in languages.items():
                print(f"添加 {self.current_editing_key} 段 {start}-{end}: '{desc}'")
                segment = self.timeline_widget.add_segment(self.current_editing_key, start, end)
                self.timeline_widget.set_segment_completed(segment, True, desc)
            
            # 找到当前帧所在的language段并立即更新显示
            current_frame = self.timeline_widget.current_frame
            for (start, end), desc in languages.items():
                if start <= current_frame <= end:
                    print(f"当前帧 {current_frame} 在 {self.current_editing_key} 段 {start}-{end} 内，立即更新显示")
                    self.update_language_display(current_frame)
                    break
            else:
                # 如果当前帧不在任何段内，显示"无"
                self.language_value_label.setText("无")
        else:
            print(f"没有找到 {self.current_editing_key} 数据")
            self.language_value_label.setText("无")
        
        print(f"----- {self.current_editing_key} 数据加载完成 -----\n")

    def on_window_added(self, start_frame, end_frame):
        """处理新增时间窗口事件"""
        print(f"新增时间窗口: {start_frame}-{end_frame}")
        # 可以在这里添加额外的处理逻辑，比如自动跳转到新窗口
        self.timeline_widget.set_current_frame(start_frame)

    def execute_batch_setting(self):
        """执行批量设置功能"""
        # 检查输入
        key_name = self.batch_key_input.text().strip()
        value = self.batch_value_input.text().strip()
        
        if not key_name:
            QMessageBox.warning(self, "输入错误", "请输入键名称")
            return
            
        if not value:
            QMessageBox.warning(self, "输入错误", "请输入要设置的值")
            return
        
        if not self.hdf5_files:
            QMessageBox.warning(self, "错误", "没有可处理的文件")
            return
        
        # 确认对话框
        file_count = len(self.hdf5_files)
        reply = QMessageBox.question(
            self, 
            "确认批量设置", 
            f"即将为 {file_count} 个HDF5文件的所有帧设置键值对:\n\n"
            f"键名称: {key_name}\n"
            f"设置值: {value}\n\n"
            f"此操作将直接修改文件，是否继续？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply != QMessageBox.Yes:
            return
        
        # 禁用按钮和输入框，防止重复操作
        self.batch_execute_btn.setEnabled(False)
        self.batch_key_input.setEnabled(False)
        self.batch_value_input.setEnabled(False)
        
        # 显示进度
        self.batch_progress.show()
        self.batch_progress.setText("正在处理...")
        
        # 处理统计
        success_count = 0
        error_count = 0
        error_files = []
        
        try:
            for i, file_path in enumerate(self.hdf5_files):
                # 更新进度显示
                self.batch_progress.setText(f"正在处理文件 {i+1}/{file_count}: {os.path.basename(file_path)}")
                
                # 强制刷新界面
                self.repaint()
                
                try:
                    # 临时打开文件进行批量设置
                    temp_model = HDF5Model(file_path)
                    
                    # 执行批量设置
                    success = temp_model.set_string_key_for_all_frames(key_name, value)
                    
                    # 关闭临时模型
                    temp_model.close()
                    
                    if success:
                        success_count += 1
                        print(f"成功处理文件: {file_path}")
                    else:
                        error_count += 1
                        error_files.append(os.path.basename(file_path))
                        print(f"处理文件失败: {file_path}")
                        
                except Exception as e:
                    error_count += 1
                    error_files.append(f"{os.path.basename(file_path)} ({str(e)})")
                    print(f"处理文件时出错: {file_path}, 错误: {e}")
            
            # 处理完成，显示结果
            self.batch_progress.setText(f"处理完成: 成功 {success_count} 个，失败 {error_count} 个")
            
            # 结果对话框
            if error_count == 0:
                QMessageBox.information(
                    self, 
                    "批量设置完成", 
                    f"成功为 {success_count} 个文件设置键值对:\n\n"
                    f"键名称: {key_name}\n"
                    f"设置值: {value}"
                )
            else:
                error_details = "\n".join(error_files[:10])  # 最多显示10个错误
                if len(error_files) > 10:
                    error_details += f"\n... 还有 {len(error_files) - 10} 个错误文件"
                    
                QMessageBox.warning(
                    self, 
                    "批量设置完成（有错误）", 
                    f"处理结果:\n"
                    f"成功: {success_count} 个文件\n"
                    f"失败: {error_count} 个文件\n\n"
                    f"失败的文件:\n{error_details}"
                )
            
            # 如果当前文件被修改，重新加载
            if self.current_file_index >= 0 and self.current_file_index < len(self.hdf5_files):
                current_file = self.hdf5_files[self.current_file_index]
                print(f"重新加载当前文件: {current_file}")
                self.load_hdf5_file(current_file)
                
        except Exception as e:
            QMessageBox.critical(self, "批量设置错误", f"批量设置过程中发生错误: {e}")
            self.batch_progress.setText(f"操作失败: {str(e)}")
            
        finally:
            # 恢复界面状态
            self.batch_execute_btn.setEnabled(True)
            self.batch_key_input.setEnabled(True)
            self.batch_value_input.setEnabled(True)