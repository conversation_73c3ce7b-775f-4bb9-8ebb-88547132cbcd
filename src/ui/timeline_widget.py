# -*- coding: utf-8 -*-
from PyQt5.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QLabel, QSlider, QPushButton, QComboBox, QSpinBox, QInputDialog, QMessageBox, QFrame, QSizePolicy
from PyQt5.QtCore import Qt, pyqtSignal, QRect, QTimer, QPoint, QDateTime
from PyQt5.QtGui import QPainter, QColor, QBrush, QPen, QMouseEvent
from typing import Dict, List, Tuple, Optional, Set, Any
import random
import hashlib


class TimelineSegment:
    """表示时间轴上的一个段"""
    
    def __init__(self, start: int, end: int, color: QColor, key: str, data_value: Any = None):
        """
        初始化时间轴段
        
        Args:
            start: 起始帧索引
            end: 结束帧索引
            color: 段的颜色
            key: 关联的键
            data_value: 段对应的实际数据值
        """
        self.start = start
        self.end = end
        self.color = color
        self.key = key
        self.data_value = data_value  # 存储实际的数据值
        self.completed = False  # 标记是否已完成标注
        self.subtask = ""  # 段的language描述
        self.hovered = False  # 标记是否被鼠标悬停
    
    def get_color(self, highlight: bool = False) -> QColor:
        """根据状态返回合适的颜色"""
        base_color = self.color
        
        # 如果有数据值，基于数据值生成颜色
        if self.data_value is not None:
            base_color = self._generate_color_from_value(self.data_value)
        # 对于所有已完成的段，使用基于描述文本生成的颜色
        elif self.completed and self.subtask:
            # 使用文本的哈希值生成唯一颜色
            hash_val = hash(self.subtask)
            # 生成不同色相的鲜艳颜色
            h = (hash_val % 360) / 360.0  # 0-1范围的色相值
            s = 0.7  # 高饱和度
            v = 0.9  # 高亮度
            
            result_color = QColor()
            result_color.setHsvF(h, s, v, 1.0)
            base_color = result_color
        
        # 如果悬停或高亮，增加亮度和边框效果
        if self.hovered or highlight:
            # 增加亮度，但保持色调
            h, s, v, a = base_color.getHsvF()
            v = min(1.0, v * 1.3)  # 增加30%亮度
            bright_color = QColor()
            bright_color.setHsvF(h, s, v, a)
            return bright_color
            
        return base_color
    
    def _generate_color_from_value(self, value: Any) -> QColor:
        """
        基于数据值生成颜色
        
        Args:
            value: 数据值
            
        Returns:
            生成的颜色
        """
        # 将值转换为字符串用于哈希
        value_str = str(value)
        
        # 使用MD5哈希确保颜色的一致性和分布
        hash_obj = hashlib.md5(value_str.encode('utf-8'))
        hash_hex = hash_obj.hexdigest()
        
        # 从哈希值中提取RGB分量
        r = int(hash_hex[0:2], 16)
        g = int(hash_hex[2:4], 16) 
        b = int(hash_hex[4:6], 16)
        
        # 调整颜色使其更加鲜艳和可区分
        # 转换到HSV空间进行调整
        color = QColor(r, g, b)
        h, s, v, a = color.getHsvF()
        
        # 确保饱和度和亮度在合适的范围内
        s = max(0.6, min(0.9, s))  # 饱和度在60%-90%之间
        v = max(0.7, min(0.95, v))  # 亮度在70%-95%之间
        
        result_color = QColor()
        result_color.setHsvF(h, s, v, 1.0)
        
        return result_color
    
    def get_display_text(self) -> str:
        """
        获取要在段上显示的文本
        
        Returns:
            显示文本
        """
        if self.data_value is not None:
            # 如果有数据值，显示数据值
            if isinstance(self.data_value, (int, float)):
                # 数值类型，格式化显示
                if isinstance(self.data_value, float):
                    # 浮点数，保留合适的小数位数
                    if abs(self.data_value) >= 1000:
                        return f"{self.data_value:.1e}"  # 科学计数法
                    elif abs(self.data_value) >= 1:
                        return f"{self.data_value:.2f}"  # 保留2位小数
                    else:
                        return f"{self.data_value:.3f}"  # 保留3位小数
                else:
                    # 整数
                    return str(self.data_value)
            else:
                # 字符串或其他类型
                text = str(self.data_value)
                # 限制显示长度
                if len(text) > 20:
                    return text[:17] + "..."
                return text
        elif self.completed and self.subtask:
            # 如果没有数据值但有language描述，显示描述
            return self.subtask
        else:
            # 都没有，显示键名
            return self.key


class RangeSelector:
    """表示时间轴上的范围选择器"""
    
    def __init__(self, min_val: int, max_val: int):
        """
        初始化范围选择器
        
        Args:
            min_val: 最小值
            max_val: 最大值
        """
        self.min_val = min_val
        self.max_val = max_val
        self.start = min_val
        self.end = max_val
        self.dragging_start = False
        self.dragging_end = False
        self.dragging_range = False
        self.drag_offset = 0
        self.handle_size = 10  # 滑块手柄大小
        self.active = False  # 是否激活选择器
        self.color = QColor(0, 120, 215, 150)  # 选择器颜色
        
        # 吸附相关属性
        self.snap_threshold = 3  # 吸附阈值，当距离小于这个值时会吸附
        self.snap_points = []  # 吸附点列表，存储可以吸附的帧索引
        
        # 实时显示相关属性
        self.dragging_handle = None  # 当前正在拖动的滑块（'start'或'end'）
    
    def contains_start_handle(self, pos: int, total_width: int) -> bool:
        """检查位置是否在起始滑块上"""
        handle_pos = int(self.start / self.max_val * total_width)
        return abs(pos - handle_pos) <= self.handle_size // 2
    
    def contains_end_handle(self, pos: int, total_width: int) -> bool:
        """检查位置是否在结束滑块上"""
        handle_pos = int(self.end / self.max_val * total_width)
        return abs(pos - handle_pos) <= self.handle_size // 2
    
    def contains_range(self, pos: int, total_width: int) -> bool:
        """检查位置是否在范围内"""
        start_pos = int(self.start / self.max_val * total_width)
        end_pos = int(self.end / self.max_val * total_width)
        return start_pos + self.handle_size // 2 <= pos <= end_pos - self.handle_size // 2
    
    def set_start(self, val: int):
        """设置起始值，应用吸附逻辑"""
        # 先计算未吸附的值
        raw_val = max(self.min_val, min(val, self.end - 1))
        
        # 应用吸附逻辑
        if self.snap_points:
            # 找到最近的吸附点
            closest_point = None
            min_distance = float('inf')
            
            for point in self.snap_points:
                if point >= self.min_val and point < self.end:  # 确保吸附点在有效范围内
                    distance = abs(raw_val - point)
                    if distance <= self.snap_threshold and distance < min_distance:
                        closest_point = point
                        min_distance = distance
            
            # 如果找到有效的吸附点，使用它
            if closest_point is not None:
                self.start = closest_point
                return
        
        # 如果没有找到吸附点或不需要吸附，使用原始值
        self.start = raw_val
    
    def set_end(self, val: int):
        """设置结束值，应用吸附逻辑"""
        # 先计算未吸附的值
        raw_val = max(self.start + 1, min(val, self.max_val))
        
        # 应用吸附逻辑
        if self.snap_points:
            # 找到最近的吸附点
            closest_point = None
            min_distance = float('inf')
            
            for point in self.snap_points:
                if point > self.start and point <= self.max_val:  # 确保吸附点在有效范围内
                    distance = abs(raw_val - point)
                    if distance <= self.snap_threshold and distance < min_distance:
                        closest_point = point
                        min_distance = distance
            
            # 如果找到有效的吸附点，使用它
            if closest_point is not None:
                self.end = closest_point
                return
        
        # 如果没有找到吸附点或不需要吸附，使用原始值
        self.end = raw_val
    
    def move_range(self, delta: int):
        """移动整个范围"""
        range_size = self.end - self.start
        new_start = max(self.min_val, min(self.start + delta, self.max_val - range_size))
        self.start = new_start
        self.end = new_start + range_size
    
    def set_snap_points(self, points):
        """设置吸附点列表"""
        self.snap_points = sorted(points)
        print(f"设置吸附点: {self.snap_points}")


class TimelineBar(QWidget):
    """自定义时间轴条组件"""
    
    segmentClicked = pyqtSignal(TimelineSegment)  # 段点击信号
    segmentsMultiSelected = pyqtSignal(list, str)  # 多段选择信号，增加参数传递段所属的键
    frameChanged = pyqtSignal(int)  # 帧变化信号
    rangeSelected = pyqtSignal(int, int, str)  # 范围选择信号，增加key参数
    
    def __init__(self, parent=None, key: str = ""):
        """
        初始化时间轴条
        
        Args:
            parent: 父窗口部件
            key: 时间轴关联的键
        """
        super().__init__(parent)
        self.setMinimumHeight(25)  # 减少最小高度
        self.setMaximumHeight(35)  # 设置最大高度
        self.setMinimumWidth(500)  # 设置最小宽度
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)  # 允许水平方向扩展
        self.segments = []  # 时间轴段列表
        self.total_frames = 100  # 总帧数
        self.current_frame = 0  # 当前帧
        self.completed_color = QColor(0, 128, 0)  # 已完成的颜色
        self.key = key  # 关联的键
        self.range_selector = RangeSelector(0, self.total_frames)  # 范围选择器
        self.hovered_segment = None  # 当前鼠标悬停的段
        self.selected_segments = []  # 多选模式下选中的段
        self.is_multi_select_mode = False  # 是否处于多选模式
        self.ctrl_was_pressed = False  # 记录Ctrl键是否被按下

        # 拖拽相关属性
        self.dragging_segment = None  # 当前拖拽的段
        self.drag_mode = None  # 拖拽模式：'move', 'resize_left', 'resize_right'
        self.drag_start_pos = None  # 拖拽开始位置
        self.drag_start_frame = None  # 拖拽开始时的帧位置

        # 启用鼠标跟踪
        self.setMouseTracking(True)

        # 设置焦点策略使组件能接收键盘事件
        self.setFocusPolicy(Qt.StrongFocus)
    
    def set_key(self, key: str):
        """设置关联的键"""
        self.key = key
        self.update()
    
    def set_total_frames(self, frames: int):
        """设置总帧数"""
        self.total_frames = max(1, frames)
        self.range_selector.max_val = self.total_frames
        self.update()
    
    def set_current_frame(self, frame: int):
        """设置当前帧"""
        prev_frame = self.current_frame
        self.current_frame = max(0, min(frame, self.total_frames - 1))
        
        # 重绘
        self.update()
        
        # 只有在帧实际变化时才发出信号
        if prev_frame != self.current_frame:
            print(f"发出帧变化信号: {self.current_frame}")
            # 发出帧变化信号
            self.frameChanged.emit(self.current_frame)
        else:
            print(f"帧未变化，不发送信号")
            
        print(f"*** TimelineBar({self.key}).set_current_frame完成 ***\n")
    
    def add_segment(self, segment: TimelineSegment):
        """添加时间轴段"""
        self.segments.append(segment)
        self.update()
    
    def clear_segments(self):
        """清除所有时间轴段"""
        self.segments.clear()
        self.update()
    
    def remove_segments_by_key(self, key: str):
        """移除指定键的所有段"""
        self.segments = [seg for seg in self.segments if seg.key != key]
        self.update()
    
    def set_segment_completed(self, segment: TimelineSegment, completed: bool, subtask: str = ""):
        """设置段的完成状态"""
        segment.completed = completed
        segment.subtask = subtask
        self.update()
    
    def toggle_range_selector(self, active: bool):
        """切换范围选择器的激活状态"""
        self.range_selector.active = active
        if active:
            print(f"TimelineBar({self.key}): 激活范围选择器")
        else:
            print(f"TimelineBar({self.key}): 关闭范围选择器")
        self.update()
    
    def get_selected_range(self) -> Tuple[int, int]:
        """获取选中的范围"""
        return (self.range_selector.start, self.range_selector.end)
    
    def paintEvent(self, event):
        """绘制时间轴"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        width = self.width()
        height = self.height()
        
        # 绘制背景
        painter.fillRect(0, 0, width, height, QColor(240, 240, 240))
        
        # 计算时间轴水平位置的帧索引值
        def frame_to_pos(frame):
            return int(frame / self.total_frames * width)
        
        # 绘制所有段
        for segment in self.segments:
            start_pos = frame_to_pos(segment.start)
            end_pos = frame_to_pos(segment.end)
            segment_width = end_pos - start_pos + 1
            
            # 使用段的颜色或已完成的颜色
            if segment.completed:
                color = segment.get_color()  # 使用基于描述的颜色
            else:
                color = segment.get_color()
                
            # 判断是否在多选模式下被选中
            if segment in self.selected_segments:
                # 为被选中的段绘制更明显的高亮效果
                # 使用更亮的颜色填充
                highlight_color = color.lighter(130)
                painter.fillRect(start_pos, 0, segment_width, height, highlight_color)
                
                # 绘制醒目的边框
                painter.setPen(QPen(Qt.blue, 2, Qt.SolidLine))
                painter.drawRect(start_pos, 0, segment_width, height - 1)
                
                # 绘制选中标记
                painter.setPen(QPen(Qt.white, 1))
                painter.setBrush(QBrush(Qt.blue))
                painter.drawEllipse(start_pos + segment_width // 2 - 4, height // 2 - 4, 8, 8)
            else:
                painter.fillRect(start_pos, 0, segment_width, height, color)
            
            # 如果是悬停的段或已完成的段，绘制边框和标签
            if segment == self.hovered_segment or (segment.completed and segment.subtask) or segment.data_value is not None:
                # 为当前段绘制边框
                if segment == self.hovered_segment:
                    painter.setPen(QPen(Qt.black, 2))
                    painter.drawRect(start_pos, 0, segment_width, height - 1)
                
                # 绘制文本标签（优先显示数据值，其次是language描述）
                display_text = segment.get_display_text()
                if display_text and segment_width > 30:  # 降低最小宽度要求
                    # 绘制文本标签
                    painter.setPen(Qt.black)
                    
                    # 设置字体
                    font = painter.font()
                    font.setBold(True)
                    font.setPointSize(8)
                    painter.setFont(font)
                    
                    # 裁剪文本以适应段宽度
                    text_width = painter.fontMetrics().width(display_text)
                    
                    if text_width > segment_width - 10:
                        # 如果文本太长，截断并添加省略号
                        display_text = painter.fontMetrics().elidedText(display_text, Qt.ElideRight, segment_width - 10)
                    
                    # 在段中居中绘制文本
                    text_rect = QRect(start_pos + 5, 0, segment_width - 10, height)
                    painter.drawText(text_rect, Qt.AlignVCenter | Qt.AlignLeft, display_text)
        
        # 绘制范围选择器
        if self.range_selector.active:
            start_pos = frame_to_pos(self.range_selector.start)
            end_pos = frame_to_pos(self.range_selector.end)
            
            # 绘制选择范围
            painter.fillRect(start_pos, 0, end_pos - start_pos, height, self.range_selector.color)
            
            # 绘制滑块手柄
            handle_size = self.range_selector.handle_size
            painter.setPen(QPen(Qt.black, 1))
            painter.setBrush(QBrush(Qt.white))
            
            # 起始手柄
            painter.drawRect(start_pos - handle_size // 2, 0, handle_size, height)
            
            # 结束手柄
            painter.drawRect(end_pos - handle_size // 2, 0, handle_size, height)
            
            # 绘制帧数标签
            # 设置字体
            font = painter.font()
            font.setBold(True)
            font.setPointSize(8)
            painter.setFont(font)
            
            # 起始帧标签
            start_label = f"帧: {self.range_selector.start}"
            painter.setPen(Qt.black)
            painter.drawText(start_pos - 50, height + 15, 100, 20, Qt.AlignCenter, start_label)
            
            # 结束帧标签
            end_label = f"帧: {self.range_selector.end}"
            painter.setPen(Qt.black)
            painter.drawText(end_pos - 50, height + 15, 100, 20, Qt.AlignCenter, end_label)
        
        # 绘制当前帧指示器
        current_pos = frame_to_pos(self.current_frame)
        painter.setPen(QPen(Qt.red, 2))
        painter.drawLine(current_pos, 0, current_pos, height)
        
        # 绘制键名称
        if self.key:
            painter.setPen(Qt.black)
            # 设置更小的字体
            font = painter.font()
            font.setPointSize(9)
            font.setBold(True)
            painter.setFont(font)
            painter.drawText(5, height - 5, self.key)
    
    def mousePressEvent(self, event: QMouseEvent):
        """处理鼠标按下事件"""
        if event.button() == Qt.LeftButton:
            # 获取鼠标位置对应的帧
            width = self.width()
            pos = event.x()
            frame = int(pos / width * self.total_frames)

            # 检查是否在范围选择器上
            if self.range_selector.active:
                if self.range_selector.contains_start_handle(pos, width):
                    self.range_selector.dragging_start = True
                    self.range_selector.dragging_handle = 'start'
                    # 发送帧变化信号以显示起始滑块位置的帧
                    self.frameChanged.emit(self.range_selector.start)
                    return
                elif self.range_selector.contains_end_handle(pos, width):
                    self.range_selector.dragging_end = True
                    self.range_selector.dragging_handle = 'end'
                    # 发送帧变化信号以显示结束滑块位置的帧
                    self.frameChanged.emit(self.range_selector.end)
                    return
                elif self.range_selector.contains_range(pos, width):
                    self.range_selector.dragging_range = True
                    # 使用本地的frame_to_pos函数
                    start_pos = int(self.range_selector.start / self.total_frames * width)
                    self.range_selector.drag_offset = pos - start_pos
                    return

            # 判断是否处于多选模式（按住Ctrl键）
            ctrl_pressed = event.modifiers() & Qt.ControlModifier
            self.is_multi_select_mode = ctrl_pressed

            # 记录Ctrl键状态
            self.ctrl_was_pressed = ctrl_pressed

            # 查找是否点击在段上
            clicked_segment = None
            for segment in self.segments:
                if segment.start <= frame <= segment.end:
                    clicked_segment = segment
                    break

            if clicked_segment:
                if self.is_multi_select_mode:
                    # 多选模式：切换段的选中状态
                    if clicked_segment in self.selected_segments:
                        self.selected_segments.remove(clicked_segment)
                        print(f"取消选中段 {clicked_segment.start}-{clicked_segment.end}")
                    else:
                        self.selected_segments.append(clicked_segment)
                        print(f"选中段 {clicked_segment.start}-{clicked_segment.end}")

                    # 更新UI
                    self.update()
                else:
                    # 非多选模式：检查是否开始拖拽
                    segment_start_pos = int(clicked_segment.start / self.total_frames * width)
                    segment_end_pos = int(clicked_segment.end / self.total_frames * width)

                    # 检查点击位置，确定拖拽模式
                    if abs(pos - segment_start_pos) <= 5:  # 点击左边缘
                        self.dragging_segment = clicked_segment
                        self.drag_mode = 'resize_left'
                        self.drag_start_pos = pos
                        self.drag_start_frame = frame
                        self.setCursor(Qt.SizeHorCursor)
                        print(f"开始调整段左边界: {clicked_segment.start}-{clicked_segment.end}")
                    elif abs(pos - segment_end_pos) <= 5:  # 点击右边缘
                        self.dragging_segment = clicked_segment
                        self.drag_mode = 'resize_right'
                        self.drag_start_pos = pos
                        self.drag_start_frame = frame
                        self.setCursor(Qt.SizeHorCursor)
                        print(f"开始调整段右边界: {clicked_segment.start}-{clicked_segment.end}")
                    else:  # 点击段的中间部分
                        # 选中段
                        self.selected_segments.clear()
                        self.selected_segments.append(clicked_segment)

                        # 开始移动拖拽
                        self.dragging_segment = clicked_segment
                        self.drag_mode = 'move'
                        self.drag_start_pos = pos
                        self.drag_start_frame = frame
                        self.setCursor(Qt.ClosedHandCursor)

                        # 发送段点击信号
                        self.segmentClicked.emit(clicked_segment)
                        print(f"选中并开始移动段: {clicked_segment.start}-{clicked_segment.end}")

                    self.update()
            else:
                # 未点击在段上
                if not self.is_multi_select_mode:
                    # 非多选模式，清除已选择的段
                    self.selected_segments.clear()
                    # 更新当前帧
                    self.current_frame = min(max(0, frame), self.total_frames - 1)
                    self.update()
                    # 发出当前帧变化信号
                    self.frameChanged.emit(self.current_frame)
        elif event.button() == Qt.RightButton:
            if self.selected_segments:
                self.selected_segments.clear()
                self.update()
                print("清除所有选中的段")

    def mouseDoubleClickEvent(self, event: QMouseEvent):
        """处理鼠标双击事件 - 用于编辑段的标注"""
        if event.button() == Qt.LeftButton:
            # 获取鼠标位置对应的帧
            width = self.width()
            pos = event.x()
            frame = int(pos / width * self.total_frames)

            # 查找是否双击在段上
            clicked_segment = None
            for segment in self.segments:
                if segment.start <= frame <= segment.end:
                    clicked_segment = segment
                    break

            if clicked_segment:
                # 弹出编辑对话框
                self.edit_segment_annotation(clicked_segment)

    def contextMenuEvent(self, event):
        """处理右键菜单事件"""
        # 获取鼠标位置对应的帧
        width = self.width()
        pos = event.x()
        frame = int(pos / width * self.total_frames)

        # 查找是否右键在段上
        clicked_segment = None
        for segment in self.segments:
            if segment.start <= frame <= segment.end:
                clicked_segment = segment
                break

        if clicked_segment and hasattr(clicked_segment, 'window_id'):
            self.show_segment_context_menu(event.globalPos(), clicked_segment)

    def show_segment_context_menu(self, pos, segment):
        """显示段的右键菜单"""
        from PyQt5.QtWidgets import QMenu, QAction

        menu = QMenu(self)

        # 编辑动作
        edit_action = QAction("编辑时间窗口", self)
        edit_action.triggered.connect(lambda: self.parent().edit_time_window(segment.window_id))
        menu.addAction(edit_action)

        # 删除动作
        delete_action = QAction("删除时间窗口", self)
        delete_action.triggered.connect(lambda: self.parent().delete_time_window(segment.window_id))
        menu.addAction(delete_action)

        menu.addSeparator()

        # 复制动作
        copy_action = QAction("复制描述", self)
        copy_action.triggered.connect(lambda: self.copy_segment_description(segment))
        menu.addAction(copy_action)

        menu.exec_(pos)

    def copy_segment_description(self, segment):
        """复制段的描述到剪贴板"""
        from PyQt5.QtWidgets import QApplication
        clipboard = QApplication.clipboard()
        clipboard.setText(segment.subtask or "")

    def edit_segment_annotation(self, segment):
        """编辑段的标注"""
        # 如果是时间窗口段，使用专门的编辑对话框
        if hasattr(segment, 'window_id'):
            parent_widget = self.parent()
            if hasattr(parent_widget, 'edit_time_window'):
                parent_widget.edit_time_window(segment.window_id)
                return

        # 对于其他类型的段，使用动作描述对话框
        from .action_description_dialog import ActionDescriptionDialog

        current_text = segment.subtask if segment.subtask else ""
        dialog = ActionDescriptionDialog(
            self,
            segment.start,
            segment.end,
            current_text
        )

        if dialog.exec_() == dialog.Accepted:
            new_text = dialog.get_description()
            segment.subtask = new_text.strip()
            segment.completed = bool(segment.subtask)

            # 更新时间窗口数据
            parent_widget = self.parent()
            if hasattr(parent_widget, 'time_windows'):
                for window in parent_widget.time_windows:
                    if window[0] == segment.start and window[1] == segment.end:
                        window[2] = segment.subtask
                        break

            self.update()
            print(f"更新段 {segment.start}-{segment.end} 的标注: '{segment.subtask}'")
    
    def mouseMoveEvent(self, event: QMouseEvent):
        """处理鼠标移动事件"""
        pos = event.pos()
        x = pos.x()
        width = self.width()

        # 如果范围选择器激活，处理拖动
        if self.range_selector.active:
            if self.range_selector.dragging_start:
                # 拖动起始滑块
                frame_idx = int(x / width * self.total_frames)
                self.range_selector.set_start(frame_idx)
                # 发送帧变化信号以显示起始滑块位置的帧
                self.frameChanged.emit(self.range_selector.start)
                self.update()
            elif self.range_selector.dragging_end:
                # 拖动结束滑块
                frame_idx = int(x / width * self.total_frames)
                self.range_selector.set_end(frame_idx)
                # 发送帧变化信号以显示结束滑块位置的帧
                self.frameChanged.emit(self.range_selector.end)
                self.update()
            elif self.range_selector.dragging_range:
                # 拖动整个范围
                frame_idx = int(x / width * self.total_frames)
                delta = frame_idx - self.range_selector.drag_offset
                self.range_selector.move_range(delta)
                self.range_selector.drag_offset = frame_idx
                self.update()
        elif self.dragging_segment:
            # 处理段的拖拽
            current_frame = int(x / width * self.total_frames)

            if self.drag_mode == 'move':
                # 移动整个段
                delta = current_frame - self.drag_start_frame
                new_start = max(0, self.dragging_segment.start + delta)
                new_end = min(self.total_frames - 1, self.dragging_segment.end + delta)

                # 确保段不会超出边界
                if new_end - new_start == self.dragging_segment.end - self.dragging_segment.start:
                    self.dragging_segment.start = new_start
                    self.dragging_segment.end = new_end
                    self.update()

            elif self.drag_mode == 'resize_left':
                # 调整段的左边界
                new_start = max(0, min(current_frame, self.dragging_segment.end - 1))
                self.dragging_segment.start = new_start
                self.update()

            elif self.drag_mode == 'resize_right':
                # 调整段的右边界
                new_end = min(self.total_frames - 1, max(current_frame, self.dragging_segment.start + 1))
                self.dragging_segment.end = new_end
                self.update()
        else:
            # 检查鼠标是否悬停在某个段上，并设置合适的光标
            frame_idx = int(x / width * self.total_frames)

            old_hovered = self.hovered_segment
            self.hovered_segment = None
            cursor_set = False

            for segment in self.segments:
                if segment.start <= frame_idx <= segment.end:
                    self.hovered_segment = segment
                    segment.hovered = True

                    # 检查是否在段的边缘，设置合适的光标
                    segment_start_pos = int(segment.start / self.total_frames * width)
                    segment_end_pos = int(segment.end / self.total_frames * width)

                    if abs(x - segment_start_pos) <= 5:  # 左边缘
                        self.setCursor(Qt.SizeHorCursor)
                        cursor_set = True
                    elif abs(x - segment_end_pos) <= 5:  # 右边缘
                        self.setCursor(Qt.SizeHorCursor)
                        cursor_set = True
                    else:  # 段的中间部分
                        self.setCursor(Qt.OpenHandCursor)
                        cursor_set = True

                    # 设置工具提示
                    if segment.data_value is not None:
                        # 优先显示数据值
                        self.setToolTip(f"{segment.key}: {segment.data_value} (帧: {segment.start}-{segment.end})")
                    elif segment.subtask:
                        self.setToolTip(f"{segment.subtask} (帧: {segment.start}-{segment.end})")
                    else:
                        self.setToolTip(f"{segment.key}: 帧 {segment.start}-{segment.end}")

                    break
                else:
                    segment.hovered = False

            if not cursor_set:
                # 没有悬停在段上，恢复默认光标并清除工具提示
                self.setCursor(Qt.ArrowCursor)
                self.setToolTip("")

            # 如果悬停状态发生变化，重绘
            if old_hovered != self.hovered_segment:
                if old_hovered:
                    old_hovered.hovered = False
                self.update()
    
    def leaveEvent(self, event):
        """处理鼠标离开事件"""
        # 清除悬停状态
        if self.hovered_segment:
            self.hovered_segment.hovered = False
            self.hovered_segment = None
            self.update()
    
    def mouseReleaseEvent(self, event: QMouseEvent):
        """处理鼠标释放事件"""
        if event.button() == Qt.LeftButton:
            if self.range_selector.dragging_start or self.range_selector.dragging_end or self.range_selector.dragging_range:
                self.range_selector.dragging_start = False
                self.range_selector.dragging_end = False
                self.range_selector.dragging_range = False
                self.range_selector.dragging_handle = None
                
                # 设置焦点，以便能够接收键盘事件
                self.setFocus()
    
    def keyReleaseEvent(self, event):
        """处理键盘释放事件"""
        # 检测Ctrl键松开
        if event.key() == Qt.Key_Control and self.ctrl_was_pressed:
            print("检测到Ctrl键松开")
            self.ctrl_was_pressed = False
            
            # 如果有选中的段，发送多选信号
            if self.selected_segments:
                print(f"Ctrl松开后发送多选信号，选中 {len(self.selected_segments)} 个段")
                self.segmentsMultiSelected.emit(self.selected_segments, self.key)
        
        super().keyReleaseEvent(event)
    
    def keyPressEvent(self, event):
        """处理键盘事件"""
        # 处理Enter键
        if event.key() == Qt.Key_Return or event.key() == Qt.Key_Enter:
            print(f"TimelineBar({self.key}): 检测到Enter键")
            if self.range_selector.active:
                print(f"TimelineBar({self.key}): 范围选择模式下确认选择")
                # 获取当前选择的范围
                start, end = self.range_selector.start, self.range_selector.end
                print(f"TimelineBar({self.key}): 发送范围选择信号 {start}-{end}")
                # 发送范围选择信号
                self.rangeSelected.emit(start, end, self.key)
            else:
                print(f"TimelineBar({self.key}): 不在范围选择模式下，传递给父类")
                super().keyPressEvent(event)
        # 处理ESC键，退出范围选择模式
        elif event.key() == Qt.Key_Escape and self.range_selector.active:
            print(f"TimelineBar({self.key}): ESC键，取消范围选择")
            self.toggle_range_selector(False)
        # 处理方向键，用于调整滑块位置
        elif self.range_selector.active and (event.key() == Qt.Key_Left or event.key() == Qt.Key_Right):
            step = 1 if event.key() == Qt.Key_Right else -1
            
            # 判断当前是否有正在拖动的滑块
            if self.range_selector.dragging_handle == 'start':
                # 调整起始滑块
                self.range_selector.set_start(self.range_selector.start + step)
                # 更新显示的帧
                self.frameChanged.emit(self.range_selector.start)
                self.update()
            elif self.range_selector.dragging_handle == 'end':
                # 调整结束滑块
                self.range_selector.set_end(self.range_selector.end + step)
                # 更新显示的帧
                self.frameChanged.emit(self.range_selector.end)
                self.update()
            else:
                # 如果没有正在拖动的滑块，默认调整起始滑块
                self.range_selector.dragging_handle = 'start'
                self.range_selector.set_start(self.range_selector.start + step)
                # 更新显示的帧
                self.frameChanged.emit(self.range_selector.start)
                self.update()
            print(f"TimelineBar({self.key}): 调整滑块位置，当前控制: {self.range_selector.dragging_handle}")
        # 处理上下方向键，用于切换控制的滑块
        elif self.range_selector.active and (event.key() == Qt.Key_Up or event.key() == Qt.Key_Down):
            # 切换控制的滑块
            if self.range_selector.dragging_handle == 'start':
                self.range_selector.dragging_handle = 'end'
                # 更新显示的帧
                self.frameChanged.emit(self.range_selector.end)
                print(f"TimelineBar({self.key}): 切换到控制结束滑块")
            else:
                self.range_selector.dragging_handle = 'start'
                # 更新显示的帧
                self.frameChanged.emit(self.range_selector.start)
                print(f"TimelineBar({self.key}): 切换到控制起始滑块")
            self.update()
        else:
            super().keyPressEvent(event)


class TimelineWidget(QWidget):
    """时间轴窗口部件，包含多个时间轴条和控制按钮"""

    frameChanged = pyqtSignal(int)  # 帧变化信号
    rangeSelected = pyqtSignal(int, int, str)  # 范围选择信号，包含起始、结束帧和键名
    segmentsMultiSelected = pyqtSignal(list, str)  # 多段选择信号，包含段列表和键名
    windowAdded = pyqtSignal(int, int)  # 新增时间窗口信号，包含起始和结束帧

    def __init__(self, parent=None):
        """
        初始化时间轴窗口部件

        Args:
            parent: 父窗口部件
        """
        super().__init__(parent)

        self.total_frames = 100  # 总帧数
        self.current_frame = 0  # 当前帧
        self.fps = 10  # 播放速率，帧/秒
        self.playing = False  # 是否正在播放
        self.timelines = []  # 时间轴条列表
        self.key_colors = {}  # 键到颜色的映射
        self.key_to_timeline = {}  # 键到时间轴条的映射
        self.range_selection_active = False  # 是否激活范围选择
        self.active_timeline = None  # 当前激活的时间轴（用于范围选择）

        # 时间窗口管理相关属性
        self.time_windows = []  # 时间窗口列表，每个元素为(start, end, description, id)
        self.selected_window = None  # 当前选中的时间窗口
        self.default_window_width = 50  # 默认时间窗口宽度（帧数）
        self.max_windows = 20  # 最大时间窗口数量
        self.window_id_counter = 0  # 时间窗口ID计数器
        self.min_window_width = 5  # 最小时间窗口宽度（帧数）
        self.window_overlap_allowed = False  # 是否允许时间窗口重叠

        # 创建定时器
        self.play_timer = QTimer(self)
        self.play_timer.timeout.connect(self.next_frame)

        # 设置焦点策略以接收键盘事件
        self.setFocusPolicy(Qt.StrongFocus)
        
        # 创建布局
        self.layout = QVBoxLayout(self)
        self.layout.setContentsMargins(5, 5, 5, 5) # 减少边距
        self.layout.setSpacing(5) # 减少间距
        
        # 创建控制布局
        control_layout = QHBoxLayout()
        control_layout.setSpacing(8) # 减少控件间距
        
        # 创建帧滑块
        self.frame_slider = QSlider(Qt.Horizontal)
        self.frame_slider.setMinimum(0)
        self.frame_slider.setMaximum(self.total_frames - 1)
        self.frame_slider.setValue(0)
        self.frame_slider.valueChanged.connect(self.on_slider_value_changed)
        self.frame_slider.setFixedHeight(25)  # 固定滑块高度
        self.frame_slider.setStyleSheet("""
            QSlider::groove:horizontal {
                height: 6px;
                background: #ddd;
                border-radius: 3px;
            }
            QSlider::handle:horizontal {
                background: #2a82da;
                border: 1px solid #5c5c5c;
                width: 14px;
                margin: -4px 0;
                border-radius: 7px;
            }
            QSlider::sub-page:horizontal {
                background: #5c9ced;
                border-radius: 3px;
            }
        """)
        
        # 创建帧标签
        self.frame_label = QLabel("帧: 0 / 0")
        self.frame_label.setStyleSheet("font-weight: bold; min-width: 100px; font-size: 11px;")
        self.frame_label.setFixedHeight(25)

        # 创建质量指示器
        self.quality_label = QLabel("质量: --")
        self.quality_label.setStyleSheet("font-weight: bold; min-width: 80px; font-size: 11px;")
        self.quality_label.setFixedHeight(25)
        self.quality_label.setToolTip("标注质量评分 (0-100分)")
        
        # 创建播放控制按钮组
        # 后退按钮
        self.backward_button = QPushButton("◀◀")
        self.backward_button.setFixedSize(35, 25)
        self.backward_button.clicked.connect(self.jump_backward)
        self.backward_button.setToolTip("后退10帧")

        # 上一帧按钮
        self.prev_frame_button = QPushButton("◀")
        self.prev_frame_button.setFixedSize(30, 25)
        self.prev_frame_button.clicked.connect(self.prev_frame)
        self.prev_frame_button.setToolTip("上一帧")

        # 播放/暂停按钮
        self.play_button = QPushButton("▶")
        self.play_button.setFixedSize(35, 25)
        self.play_button.clicked.connect(self.toggle_play)
        self.play_button.setToolTip("播放/暂停")

        # 下一帧按钮
        self.next_frame_button = QPushButton("▶")
        self.next_frame_button.setFixedSize(30, 25)
        self.next_frame_button.clicked.connect(self.next_frame)
        self.next_frame_button.setToolTip("下一帧")

        # 快进按钮
        self.forward_button = QPushButton("▶▶")
        self.forward_button.setFixedSize(35, 25)
        self.forward_button.clicked.connect(self.jump_forward)
        self.forward_button.setToolTip("快进10帧")

        # 设置按钮样式
        button_style = """
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 2px;
                border-radius: 3px;
                font-size: 10px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:pressed {
                background-color: #3d8b40;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """

        for button in [self.backward_button, self.prev_frame_button, self.play_button,
                      self.next_frame_button, self.forward_button]:
            button.setStyleSheet(button_style)

        # 创建FPS控制
        self.fps_spinbox = QSpinBox()
        self.fps_spinbox.setMinimum(1)
        self.fps_spinbox.setMaximum(60)
        self.fps_spinbox.setValue(self.fps)
        self.fps_spinbox.valueChanged.connect(self.on_fps_changed)
        self.fps_spinbox.setFixedSize(50, 25)  # 固定尺寸
        self.fps_spinbox.setStyleSheet("""
            QSpinBox {
                border: 1px solid #bbb;
                border-radius: 3px;
                padding: 2px;
                background: white;
                font-size: 11px;
            }
        """)

        # 创建FPS标签
        fps_label = QLabel("FPS:")
        fps_label.setStyleSheet("font-size: 11px; font-weight: bold;")
        fps_label.setFixedHeight(25)

        # 创建添加时间窗口按钮
        self.add_window_button = QPushButton("添加时间窗口")
        self.add_window_button.setFixedSize(100, 25)
        self.add_window_button.clicked.connect(self.add_time_window)
        self.add_window_button.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 4px 8px;
                border-radius: 3px;
                font-size: 11px;
            }
            QPushButton:hover {
                background-color: #0b7dda;
            }
            QPushButton:pressed {
                background-color: #0a5a9c;
            }
        """)

        # 创建保存按钮
        self.save_button = QPushButton("保存标注")
        self.save_button.setFixedSize(80, 25)
        self.save_button.clicked.connect(self.save_annotations)
        self.save_button.setStyleSheet("""
            QPushButton {
                background-color: #FF9800;
                color: white;
                border: none;
                padding: 4px 8px;
                border-radius: 3px;
                font-size: 11px;
            }
            QPushButton:hover {
                background-color: #e68900;
            }
            QPushButton:pressed {
                background-color: #cc7a00;
            }
        """)

        # 创建加载按钮
        self.load_button = QPushButton("加载标注")
        self.load_button.setFixedSize(80, 25)
        self.load_button.clicked.connect(self.load_annotations_dialog)
        self.load_button.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 4px 8px;
                border-radius: 3px;
                font-size: 11px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
            QPushButton:pressed {
                background-color: #1565C0;
            }
        """)

        # 创建清除按钮
        self.clear_button = QPushButton("清除全部")
        self.clear_button.setFixedSize(80, 25)
        self.clear_button.clicked.connect(self.clear_all_windows)
        self.clear_button.setStyleSheet("""
            QPushButton {
                background-color: #F44336;
                color: white;
                border: none;
                padding: 4px 8px;
                border-radius: 3px;
                font-size: 11px;
            }
            QPushButton:hover {
                background-color: #D32F2F;
            }
            QPushButton:pressed {
                background-color: #C62828;
            }
        """)

        # 创建验证按钮
        self.validate_button = QPushButton("验证标注")
        self.validate_button.setFixedSize(80, 25)
        self.validate_button.clicked.connect(self.validate_annotations)
        self.validate_button.setStyleSheet("""
            QPushButton {
                background-color: #9C27B0;
                color: white;
                border: none;
                padding: 4px 8px;
                border-radius: 3px;
                font-size: 11px;
            }
            QPushButton:hover {
                background-color: #7B1FA2;
            }
            QPushButton:pressed {
                background-color: #6A1B9A;
            }
        """)

        # 将控件添加到控制布局
        # 播放控制按钮组
        control_layout.addWidget(self.backward_button)
        control_layout.addWidget(self.prev_frame_button)
        control_layout.addWidget(self.play_button)
        control_layout.addWidget(self.next_frame_button)
        control_layout.addWidget(self.forward_button)

        # 分隔符
        control_layout.addWidget(QLabel("|"))

        # FPS控制
        control_layout.addWidget(fps_label)
        control_layout.addWidget(self.fps_spinbox)

        # 分隔符
        control_layout.addWidget(QLabel("|"))

        # 时间窗口控制
        control_layout.addWidget(self.add_window_button)
        control_layout.addWidget(self.save_button)
        control_layout.addWidget(self.load_button)
        control_layout.addWidget(self.clear_button)
        control_layout.addWidget(self.validate_button)

        # 滑块和标签
        control_layout.addWidget(self.frame_slider, 1) # 1是拉伸因子
        control_layout.addWidget(self.frame_label)
        control_layout.addWidget(self.quality_label)
        
        # 将控制布局添加到主布局
        self.layout.addLayout(control_layout)
        
        # 时间轴区域（移除分隔线以节省空间）
        self.timelines_layout = QVBoxLayout()
        self.timelines_layout.setSpacing(3) # 减少间距
        self.timelines_layout.setContentsMargins(0, 5, 0, 0) # 减少边距
        self.layout.addLayout(self.timelines_layout, 1) # 添加1的拉伸因子，使所有时间轴填充可用空间
        
        # 添加键控提示（使用更小的字体）
        key_hint_label = QLabel("提示: 按Enter键进入范围选择模式")
        key_hint_label.setAlignment(Qt.AlignCenter)
        key_hint_label.setStyleSheet("color: #666; font-style: italic; font-size: 10px;")
        key_hint_label.setFixedHeight(20)  # 固定高度
        self.layout.addWidget(key_hint_label)
        
        # 设置焦点策略，使组件可以接收键盘事件
        self.setFocusPolicy(Qt.StrongFocus)
    
    def set_total_frames(self, frames: int):
        """设置总帧数"""
        self.total_frames = max(1, frames)
        self.frame_slider.setMaximum(self.total_frames - 1)
        
        for timeline in self.timelines:
            timeline.set_total_frames(self.total_frames)
            
        self.update_frame_label()
    
    def set_current_frame(self, frame: int):
        """设置当前帧"""
        prev_frame = self.current_frame
        self.current_frame = max(0, min(frame, self.total_frames - 1))
        
        # 更新帧滑块的值（避免循环触发）
        if self.frame_slider.value() != self.current_frame:
            self.frame_slider.blockSignals(True)
            self.frame_slider.setValue(self.current_frame)
            self.frame_slider.blockSignals(False)
        
        # 更新帧标签
        self.update_frame_label()

        # 更新播放控制按钮状态
        self.update_playback_button_states()

        # 更新所有时间轴的当前帧
        for timeline in self.timelines:
            if timeline.current_frame != self.current_frame:
                # 阻止循环调用
                timeline.blockSignals(True)
                timeline.set_current_frame(self.current_frame)
                timeline.blockSignals(False)
        
        # 重绘
        self.update()
        
        # 只有在帧实际变化时才发出信号
        if prev_frame != self.current_frame:
            print(f"发出帧变化信号: {self.current_frame}")
            # 发出帧变化信号
            self.frameChanged.emit(self.current_frame)
        else:
            print(f"帧未变化，不发送信号")
        
        print(f"*** TimelineWidget.set_current_frame完成 ***\n")
    
    def update_frame_label(self):
        """更新帧标签"""
        self.frame_label.setText(f"帧: {self.current_frame} / {self.total_frames - 1}")
    
    def on_slider_value_changed(self, value: int):
        """处理滑块值变化"""
        print(f"\n@@@ 滑块值变化: {value} @@@")
        
        # 保存当前帧用于对比
        old_frame = self.current_frame
        
        # 设置当前帧
        self.set_current_frame(value)
        
        # 检查帧是否实际更新
        if old_frame != self.current_frame:
            print(f"帧已更新: {old_frame} -> {self.current_frame}")
        else:
            print(f"帧未变化: 仍为 {self.current_frame}")
        
        # 无论是否帧已经变化，都强制发送信号
        print(f"强制发送帧变化信号: {value}")
        self.frameChanged.emit(value)
        print(f"@@@ 滑块值变化处理完成 @@@\n")
    
    def toggle_play(self):
        """切换播放状态"""
        self.playing = not self.playing

        if self.playing:
            self.play_button.setText("⏸")
            self.play_button.setToolTip("暂停")
            # 启动定时器
            interval = 1000 // self.fps  # 计算毫秒间隔
            self.play_timer.start(interval)
        else:
            self.play_button.setText("▶")
            self.play_button.setToolTip("播放")
            # 停止定时器
            self.play_timer.stop()

        # 更新按钮状态
        self.update_playback_button_states()

    def prev_frame(self):
        """上一帧"""
        if self.current_frame > 0:
            self.set_current_frame(self.current_frame - 1)

    def next_frame(self):
        """下一帧"""
        if self.current_frame < self.total_frames - 1:
            self.set_current_frame(self.current_frame + 1)

    def jump_backward(self):
        """后退多帧"""
        jump_frames = 10  # 可以设置为可配置的值
        new_frame = max(0, self.current_frame - jump_frames)
        self.set_current_frame(new_frame)

    def jump_forward(self):
        """快进多帧"""
        jump_frames = 10  # 可以设置为可配置的值
        new_frame = min(self.total_frames - 1, self.current_frame + jump_frames)
        self.set_current_frame(new_frame)

    def update_playback_button_states(self):
        """更新播放控制按钮的状态"""
        # 根据当前帧位置启用/禁用按钮
        self.backward_button.setEnabled(self.current_frame > 0)
        self.prev_frame_button.setEnabled(self.current_frame > 0)
        self.next_frame_button.setEnabled(self.current_frame < self.total_frames - 1)
        self.forward_button.setEnabled(self.current_frame < self.total_frames - 1)
    
    def on_fps_changed(self, value: int):
        """处理FPS变化"""
        self.fps = value
        if self.playing:
            # 更新定时器间隔
            interval = 1000 // self.fps
            self.play_timer.setInterval(interval)
    

    
    def toggle_range_selection(self, checked: bool):
        """切换范围选择模式"""
        self.range_selection_active = checked
        
        if checked:
            print("进入范围选择模式")
            self.range_select_button.setText("取消范围选择")
            # 设置初始选择范围为当前帧附近
            start = max(0, self.current_frame - 10)
            end = min(self.current_frame + 10, self.total_frames - 1)
            
            # 检测language段的边界作为吸附点
            snap_points = self.detect_language_boundaries()
            print(f"检测到的language边界吸附点: {snap_points}")
            
            # 如果指定了活动时间轴，只在该时间轴上激活范围选择器
            if self.active_timeline:
                self.active_timeline.range_selector.start = start
                self.active_timeline.range_selector.end = end
                self.active_timeline.range_selector.set_snap_points(snap_points)
                self.active_timeline.toggle_range_selector(True)
                # 调整时间轴区域大小，确保可以显示帧数标签
                self.active_timeline.setMinimumHeight(50)
                print(f"在时间轴 {self.active_timeline.key} 上激活范围选择器")
            else:
                # 否则在所有时间轴上激活
                for timeline in self.timelines:
                    timeline.range_selector.start = start
                    timeline.range_selector.end = end
                    timeline.range_selector.set_snap_points(snap_points)
                    timeline.toggle_range_selector(True)
                    # 调整时间轴区域大小，确保可以显示帧数标签
                    timeline.setMinimumHeight(50)
                print("在所有时间轴上激活范围选择器")
        else:
            print("退出范围选择模式")
            self.range_select_button.setText("范围选择")
            self.active_timeline = None
            # 在所有时间轴上关闭范围选择器
            for timeline in self.timelines:
                timeline.toggle_range_selector(False)
                # 恢复时间轴高度
                timeline.setMinimumHeight(30)
            print("在所有时间轴上关闭范围选择器")
        
        # 禁用播放按钮，防止在选择范围时播放
        self.play_button.setEnabled(not checked)
    
    def add_timeline(self, key: str = "") -> TimelineBar:
        """
        添加一个新的时间轴条
        
        Args:
            key: 关联的键
            
        Returns:
            创建的时间轴条
        """
        timeline = TimelineBar(self, key)
        timeline.set_total_frames(self.total_frames)
        timeline.set_current_frame(self.current_frame)
        # 将 TimelineBar 的 frameChanged 信号连接到 TimelineWidget 的 set_current_frame 方法
        # 这样当用户点击任何一个时间轴时，所有时间轴都会同步更新
        timeline.frameChanged.connect(self.set_current_frame)
        
        # 连接rangeSelected信号，包含键名
        timeline.rangeSelected.connect(self.on_range_selected)
        
        # 连接多选信号
        timeline.segmentsMultiSelected.connect(self.on_segments_multi_selected)
        
        self.timelines_layout.addWidget(timeline)
        self.timelines.append(timeline)
        
        if key:
            self.key_to_timeline[key] = timeline
        
        return timeline
    
    def on_range_selected(self, start: int, end: int, key: str):
        """
        处理范围选择
        
        Args:
            start: 起始帧
            end: 结束帧
            key: 关联的键
        """
        print(f"TimelineWidget: 接收到范围选择信号，键 '{key}'，范围 {start}-{end}")
        
        # 关闭范围选择模式
        self.toggle_range_selection(False)
        
        # 发出范围选择信号
        self.rangeSelected.emit(start, end, key)
    
    def on_segments_multi_selected(self, segments, key):
        """
        处理多段选择
        
        Args:
            segments: 选中的段列表
            key: 段所属的键
        """
        # 不需要查找发送信号的时间轴，因为key已经传递过来了
        # 发出多段选择信号，包含键名
        self.segmentsMultiSelected.emit(segments, key)
        print(f"TimelineWidget: 发送多选信号，键 '{key}'，选中 {len(segments)} 个段")
    
    def clear_segments(self):
        """清除所有时间轴上的所有段"""
        for timeline in self.timelines:
            timeline.clear_segments()
        
        # 清空时间轴映射并移除所有时间轴
        for timeline in list(self.timelines):
            self.timelines_layout.removeWidget(timeline)
            timeline.hide()
            timeline.deleteLater()
        
        self.timelines.clear()
        self.key_to_timeline.clear()
    
    def add_timeline_for_language(self) -> TimelineBar:
        """
        专门为language创建时间轴
        
        Returns:
            创建的时间轴条
        """
        return self.add_timeline_for_key("language")
    
    def add_timeline_for_key(self, key: str) -> TimelineBar:
        """
        为指定键创建时间轴
        
        Args:
            key: 键名
            
        Returns:
            创建的时间轴条
        """
        # 如果已经存在该键的时间轴，直接返回
        if key in self.key_to_timeline:
            return self.key_to_timeline[key]
        
        # 为该键设置特殊的颜色
        if key not in self.key_colors:
            if key == "language":
                self.key_colors[key] = QColor(60, 180, 75)  # 醒目的绿色
            else:
                # 为其他键生成基于键名的固定颜色
                hash_val = int(hashlib.md5(key.encode()).hexdigest()[:6], 16)
                r = 100 + (hash_val & 0x7F)  # 100-227
                g = 100 + ((hash_val >> 8) & 0x7F)  # 100-227
                b = 100 + ((hash_val >> 16) & 0x7F)  # 100-227
                self.key_colors[key] = QColor(r, g, b)
        
        # 先检查是否已经存在标签，避免重复创建
        existing_label = None
        label_text = f"{key}标签轨道:"
        for i in range(self.timelines_layout.count()):
            item = self.timelines_layout.itemAt(i)
            if item and item.widget():
                widget = item.widget()
                if isinstance(widget, QLabel) and label_text in widget.text():
                    existing_label = widget
                    break
        
        # 创建特殊的键时间轴
        timeline = self.add_timeline(key)
        
        # 将该键时间轴移到合适的位置
        self.timelines_layout.removeWidget(timeline)
        
        # 如果是language键，放在最上方；否则按字母顺序排列
        if key == "language":
            insert_index = 0 if not existing_label else 1
        else:
            # 找到合适的插入位置（按字母顺序）
            insert_index = self.timelines_layout.count()
            for i in range(self.timelines_layout.count()):
                item = self.timelines_layout.itemAt(i)
                if item and item.widget():
                    widget = item.widget()
                    if hasattr(widget, 'key') and widget.key > key:
                        insert_index = i
                        break
        
        self.timelines_layout.insertWidget(insert_index, timeline)
        
        # 只有在没有已存在的标签时才创建新标签
        if not existing_label:
            # 为该键时间轴添加标题标签
            key_label = QLabel(f"{key}标签轨道:")
            key_label.setStyleSheet(f"""
                font-weight: bold; 
                color: #{self.key_colors[key].red():02x}{self.key_colors[key].green():02x}{self.key_colors[key].blue():02x}; 
                font-size: 13px; 
                padding: 5px; 
                background-color: #f0f8ff;
                border-radius: 4px;
                margin-top: 10px;
            """)
            key_label.setAlignment(Qt.AlignCenter)
            
            # 插入标签到时间轴上方
            label_insert_index = insert_index if insert_index > 0 else 0
            self.timelines_layout.insertWidget(label_insert_index, key_label)
        
        # 设置timeline的最小高度，使其更加明显
        timeline.setMinimumHeight(40)
        
        return timeline
    
    def add_segment(self, key: str, start: int, end: int) -> TimelineSegment:
        """
        添加一个段到对应键的时间轴
        
        Args:
            key: 关联的键
            start: 起始帧
            end: 结束帧
            
        Returns:
            创建的段对象
        """
        # 确保键有一个固定的颜色
        if key not in self.key_colors:
            if key == "language":
                # 为language使用特殊的颜色
                self.key_colors[key] = QColor(60, 180, 75)  # 醒目的绿色
            else:
                # 随机生成一个颜色，但避免太亮或太暗
                r = random.randint(50, 200)
                g = random.randint(50, 200)
                b = random.randint(50, 200)
                self.key_colors[key] = QColor(r, g, b)
        
        # 如果是language键，确保它总是显示在最上方
        if key == "language":
            # 使用专门的方法创建language时间轴
            if key not in self.key_to_timeline:
                self.add_timeline_for_language()
        else:
            # 如果键没有对应的时间轴，创建一个
            if key not in self.key_to_timeline:
                timeline = self.add_timeline(key)
                self.key_to_timeline[key] = timeline
        
        # 获取对应的时间轴
        timeline = self.key_to_timeline[key]
        
        # 创建段
        segment = TimelineSegment(start, end, self.key_colors[key], key)
        timeline.add_segment(segment)
        
        return segment
    
    def add_segment_with_value(self, key: str, start: int, end: int, value: Any) -> TimelineSegment:
        """
        添加一个带有数据值的段到对应键的时间轴
        
        Args:
            key: 关联的键
            start: 起始帧
            end: 结束帧
            value: 数据值
            
        Returns:
            创建的段对象
        """
        # 确保键有一个固定的颜色（作为备用颜色）
        if key not in self.key_colors:
            if key == "language":
                # 为language使用特殊的颜色
                self.key_colors[key] = QColor(60, 180, 75)  # 醒目的绿色
            else:
                # 随机生成一个颜色，但避免太亮或太暗
                r = random.randint(50, 200)
                g = random.randint(50, 200)
                b = random.randint(50, 200)
                self.key_colors[key] = QColor(r, g, b)
        
        # 如果是language键，确保它总是显示在最上方
        if key == "language":
            # 使用专门的方法创建language时间轴
            if key not in self.key_to_timeline:
                self.add_timeline_for_language()
        else:
            # 如果键没有对应的时间轴，创建一个
            if key not in self.key_to_timeline:
                timeline = self.add_timeline(key)
                self.key_to_timeline[key] = timeline
        
        # 获取对应的时间轴
        timeline = self.key_to_timeline[key]
        
        # 创建带有数据值的段
        segment = TimelineSegment(start, end, self.key_colors[key], key, value)
        timeline.add_segment(segment)
        
        return segment
    
    def set_segment_completed(self, segment: TimelineSegment, completed: bool, subtask: str = ""):
        """设置段的完成状态"""
        if segment.key in self.key_to_timeline:
            timeline = self.key_to_timeline[segment.key]
            for seg in timeline.segments:
                if seg is segment:
                    timeline.set_segment_completed(seg, completed, subtask)
                    return
    
    def remove_segments_by_key(self, key: str):
        """移除指定键的所有段"""
        if key in self.key_to_timeline:
            timeline = self.key_to_timeline[key]
            timeline.clear_segments()
            
            # 隐藏并移除对应的时间轴
            index = self.timelines.index(timeline)
            self.timelines.remove(timeline)
            timeline.hide()
            self.timelines_layout.removeWidget(timeline)
            
            # 如果是language键，还需要移除上方的标签
            if key == "language":
                # 查找language标签并移除
                for i in range(self.timelines_layout.count()):
                    item = self.timelines_layout.itemAt(i)
                    if item and item.widget():
                        widget = item.widget()
                        if isinstance(widget, QLabel) and "Language标签轨道" in widget.text():
                            widget.hide()
                            self.timelines_layout.removeWidget(widget)
                            widget.deleteLater()
                            break
            
            del self.key_to_timeline[key]
    
    def get_current_frame(self) -> int:
        """获取当前帧索引"""
        return self.current_frame
    
    def set_frame_count(self, frames: int):
        """设置总帧数（兼容接口）"""
        self.set_total_frames(frames)
    
    def reset_segments(self, keys_to_preserve=None):
        """
        重置所有段
        
        Args:
            keys_to_preserve: 要保留的键列表，不会被重置
        """
        if keys_to_preserve is None:
            keys_to_preserve = []
            
        print(f"重置时间轴段，保留键: {keys_to_preserve}")
        
        # 保存要保留的时间轴
        preserved_timelines = {}
        for key in keys_to_preserve:
            if key in self.key_to_timeline:
                preserved_timelines[key] = self.key_to_timeline[key]
                # 从时间轴列表中移除，以避免被清除
                if self.key_to_timeline[key] in self.timelines:
                    self.timelines.remove(self.key_to_timeline[key])
        
        # 清除其他所有时间轴
        self.clear_segments()
        
        # 恢复保留的时间轴
        for key, timeline in preserved_timelines.items():
            self.key_to_timeline[key] = timeline
            self.timelines.append(timeline)
        
        # 如果没有时间轴，则添加一个默认的
        if not self.timelines:
            timeline = self.add_timeline_for_language()
            
            # 连接segmentClicked信号到父窗口（如果存在）
            if self.parent() and hasattr(self.parent(), 'on_segment_clicked'):
                timeline.segmentClicked.connect(self.parent().on_segment_clicked)
    
    def keyPressEvent(self, event):
        """处理键盘事件"""
        # 处理Enter键
        if event.key() == Qt.Key_Return or event.key() == Qt.Key_Enter:
            print(f"TimelineWidget: 检测到Enter键")
            if self.range_selection_active:
                # 已经在范围选择模式，但这里不需要处理，因为各个TimelineBar会自己处理
                print("TimelineWidget: 已在范围选择模式，交给活动的时间轴处理")
                super().keyPressEvent(event)
            else:
                # 进入范围选择模式
                print("TimelineWidget: 进入范围选择模式")
                
                # 优先寻找当前编辑键的时间轴
                target_timeline = None
                
                # 从父窗口获取当前编辑键
                if self.parent() and hasattr(self.parent(), 'current_editing_key'):
                    current_editing_key = self.parent().current_editing_key
                    print(f"TimelineWidget: 当前编辑键为 {current_editing_key}")
                    
                    # 查找当前编辑键的时间轴
                    if current_editing_key in self.key_to_timeline:
                        timeline = self.key_to_timeline[current_editing_key]
                        if timeline.isVisible():
                            target_timeline = timeline
                            print(f"TimelineWidget: 找到当前编辑键 {current_editing_key} 的可见时间轴")
                        else:
                            print(f"TimelineWidget: 当前编辑键 {current_editing_key} 的时间轴不可见")
                
                # 如果没有找到当前编辑键的时间轴，寻找任何可见的时间轴
                if not target_timeline:
                    for timeline in self.timelines:
                        if timeline.isVisible():
                            target_timeline = timeline
                            print(f"TimelineWidget: 使用可见的时间轴 {timeline.key}")
                            break
                
                if target_timeline:
                    self.active_timeline = target_timeline
                    print(f"TimelineWidget: 设置活动时间轴为 {self.active_timeline.key}")
                else:
                    print("TimelineWidget: 没有找到可用的时间轴")
                    return
                
                # 激活范围选择模式
                self.toggle_range_selection(True)
                
                # 设置初始范围在当前帧附近
                start = max(0, self.current_frame - 5)
                end = min(self.total_frames - 1, self.current_frame + 5)
                self.active_timeline.range_selector.start = start
                self.active_timeline.range_selector.end = end
                self.active_timeline.update()
                
                # 设置焦点到活动时间轴
                self.active_timeline.setFocus()
        
        # 处理ESC键，退出范围选择模式
        elif event.key() == Qt.Key_Escape and self.range_selection_active:
            print("TimelineWidget: ESC键，退出范围选择模式")
            self.toggle_range_selection(False)
        else:
            # 其他键传递给父类
            super().keyPressEvent(event)

    def detect_language_boundaries(self):
        """
        检测当前编辑键时间轴段的边界作为吸附点
        
        Returns:
            段边界帧索引列表
        """
        snap_points = []
        
        # 优先检测当前编辑键的时间轴
        target_timeline = None
        
        # 从父窗口获取当前编辑键
        if self.parent() and hasattr(self.parent(), 'current_editing_key'):
            current_editing_key = self.parent().current_editing_key
            print(f"detect_language_boundaries: 当前编辑键为 {current_editing_key}")
            
            # 查找当前编辑键的时间轴
            if current_editing_key in self.key_to_timeline:
                timeline = self.key_to_timeline[current_editing_key]
                if timeline.isVisible():
                    target_timeline = timeline
                    print(f"detect_language_boundaries: 使用当前编辑键 {current_editing_key} 的时间轴")
        
        # 如果没有找到当前编辑键的时间轴，使用第一个可见的时间轴
        if not target_timeline:
            for timeline in self.timelines:
                if timeline.isVisible():
                    target_timeline = timeline
                    print(f"detect_language_boundaries: 使用可见的时间轴 {timeline.key}")
                    break
        
        # 收集目标时间轴所有段的边界
        if target_timeline:
            for segment in target_timeline.segments:
                snap_points.append(segment.start)
                snap_points.append(segment.end)
            print(f"detect_language_boundaries: 从 {target_timeline.key} 收集到 {len(snap_points)} 个边界点")
        else:
            print("detect_language_boundaries: 没有找到可用的时间轴")
        
        # 去重并排序
        return sorted(list(set(snap_points)))

    def add_time_window(self):
        """添加新的时间窗口"""
        if len(self.time_windows) >= self.max_windows:
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.warning(self, "警告", f"最多只能添加{self.max_windows}个时间窗口")
            return

        # 计算新时间窗口的起始位置
        start_frame = self._calculate_new_window_start()
        end_frame = min(start_frame + self.default_window_width, self.total_frames - 1)

        # 验证窗口是否有效
        if not self._validate_window_bounds(start_frame, end_frame):
            return

        # 检查重叠
        if not self.window_overlap_allowed and self._check_window_overlap(start_frame, end_frame):
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.warning(self, "警告", "新时间窗口与现有窗口重叠，请选择其他位置")
            return

        # 创建新的时间窗口
        window_id = self._generate_window_id()
        new_window = [start_frame, end_frame, "", window_id]  # [start, end, description, id]
        self.time_windows.append(new_window)

        # 在时间轴上创建对应的段
        self.create_window_segment(new_window)

        # 发出信号通知主窗口
        self.windowAdded.emit(start_frame, end_frame)

        # 更新质量指示器
        self.update_quality_indicator()

        print(f"添加新时间窗口: {start_frame}-{end_frame} (ID: {window_id})")

    def _calculate_new_window_start(self):
        """计算新时间窗口的起始位置"""
        if self.time_windows:
            # 找到最适合的位置（避免重叠）
            if not self.window_overlap_allowed:
                # 找到最大的空隙
                sorted_windows = sorted(self.time_windows, key=lambda w: w[0])

                # 检查开头是否有空间
                if sorted_windows[0][0] >= self.default_window_width:
                    return 0

                # 检查窗口之间的空隙
                for i in range(len(sorted_windows) - 1):
                    gap_start = sorted_windows[i][1] + 1
                    gap_end = sorted_windows[i + 1][0] - 1
                    if gap_end - gap_start + 1 >= self.default_window_width:
                        return gap_start

                # 检查最后一个窗口之后是否有空间
                last_end = sorted_windows[-1][1]
                if self.total_frames - 1 - last_end >= self.default_window_width:
                    return last_end + 1

                # 如果没有足够空间，从当前帧开始
                return self.current_frame
            else:
                # 允许重叠时，从最后一个窗口的结束位置开始
                last_window = max(self.time_windows, key=lambda w: w[1])
                return last_window[1]
        else:
            # 如果没有时间窗口，从当前帧开始
            return self.current_frame

    def _validate_window_bounds(self, start_frame, end_frame):
        """验证时间窗口边界是否有效"""
        if start_frame < 0 or end_frame >= self.total_frames:
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.warning(self, "警告", "时间窗口超出视频范围")
            return False

        if end_frame - start_frame + 1 < self.min_window_width:
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.warning(self, "警告", f"时间窗口太小，最小宽度为{self.min_window_width}帧")
            return False

        if start_frame >= self.total_frames - 1:
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.warning(self, "警告", "已到达视频结尾，无法添加更多时间窗口")
            return False

        return True

    def _check_window_overlap(self, start_frame, end_frame, exclude_window_id=None):
        """检查时间窗口是否与现有窗口重叠"""
        for window in self.time_windows:
            window_id = window[3] if len(window) > 3 else None
            if exclude_window_id and window_id == exclude_window_id:
                continue

            window_start, window_end = window[0], window[1]
            if not (end_frame < window_start or start_frame > window_end):
                return True
        return False

    def _generate_window_id(self):
        """生成唯一的时间窗口ID"""
        self.window_id_counter += 1
        return self.window_id_counter

    def create_window_segment(self, window):
        """为时间窗口创建时间轴段"""
        start, end, description = window[0], window[1], window[2]
        window_id = window[3] if len(window) > 3 else None

        # 确保有一个时间轴来显示窗口
        if not self.timelines:
            timeline = self.add_timeline("annotation")
        else:
            timeline = self.timelines[0]  # 使用第一个时间轴

        # 创建段
        color = QColor(100, 150, 200) if not description else QColor(50, 150, 50)
        segment = TimelineSegment(start, end, color, "annotation")
        segment.subtask = description
        segment.completed = bool(description)

        # 存储窗口ID到段中
        if window_id:
            segment.window_id = window_id

        timeline.add_segment(segment)

        return segment

    def edit_time_window(self, window_id):
        """编辑指定ID的时间窗口"""
        window = self.get_window_by_id(window_id)
        if not window:
            return

        from .time_window_dialog import TimeWindowDialog

        dialog = TimeWindowDialog(self)
        dialog.set_window_data(window[0], window[1], window[2])

        if dialog.exec_() == dialog.Accepted:
            new_start, new_end, new_description = dialog.get_window_data()

            # 验证新的边界
            if not self._validate_window_bounds(new_start, new_end):
                return

            # 检查重叠（排除当前窗口）
            if not self.window_overlap_allowed and self._check_window_overlap(new_start, new_end, window_id):
                from PyQt5.QtWidgets import QMessageBox
                QMessageBox.warning(self, "警告", "修改后的时间窗口与其他窗口重叠")
                return

            # 更新窗口数据
            window[0] = new_start
            window[1] = new_end
            window[2] = new_description

            # 更新时间轴段
            self._update_window_segment(window)

            # 更新质量指示器
            self.update_quality_indicator()

            print(f"编辑时间窗口 {window_id}: {new_start}-{new_end}, '{new_description}'")

    def delete_time_window(self, window_id):
        """删除指定ID的时间窗口"""
        window = self.get_window_by_id(window_id)
        if not window:
            return

        from PyQt5.QtWidgets import QMessageBox
        reply = QMessageBox.question(
            self, "确认删除",
            f"确定要删除时间窗口 {window[0]}-{window[1]} 吗？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # 从列表中移除窗口
            self.time_windows.remove(window)

            # 从时间轴中移除对应的段
            self._remove_window_segment(window)

            print(f"删除时间窗口 {window_id}: {window[0]}-{window[1]}")

    def get_window_by_id(self, window_id):
        """根据ID获取时间窗口"""
        for window in self.time_windows:
            if len(window) > 3 and window[3] == window_id:
                return window
        return None

    def _update_window_segment(self, window):
        """更新时间窗口对应的时间轴段"""
        window_id = window[3] if len(window) > 3 else None

        # 找到并移除旧段
        for timeline in self.timelines:
            timeline.segments = [seg for seg in timeline.segments
                               if not (hasattr(seg, 'window_id') and seg.window_id == window_id)]

        # 创建新段
        self.create_window_segment(window)

        # 重绘所有时间轴
        for timeline in self.timelines:
            timeline.update()

    def _remove_window_segment(self, window):
        """移除时间窗口对应的时间轴段"""
        window_id = window[3] if len(window) > 3 else None

        # 从所有时间轴中移除对应的段
        for timeline in self.timelines:
            timeline.segments = [seg for seg in timeline.segments
                               if not (hasattr(seg, 'window_id') and seg.window_id == window_id)]
            timeline.update()

    def save_annotations(self):
        """保存标注数据（使用增强的导出对话框）"""
        if not self.time_windows:
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.information(self, "提示", "没有时间窗口需要保存")
            return

        # 准备保存数据
        annotations = []
        for window in self.time_windows:
            start, end, description = window[0], window[1], window[2]
            window_id = window[3] if len(window) > 3 else None

            annotation = {
                "start_frame": start,
                "end_frame": end,
                "description": description,
                "duration_frames": end - start + 1
            }

            if window_id:
                annotation["window_id"] = window_id

            annotations.append(annotation)

        # 在导出前进行验证
        from ..utils.annotation_validator import AnnotationValidator
        from PyQt5.QtWidgets import QMessageBox

        validator = AnnotationValidator()
        results = validator.validate_annotations(annotations, self.total_frames)
        summary = validator.get_validation_summary(results)

        # 如果有严重错误，询问用户是否继续
        if summary['has_critical_issues']:
            reply = QMessageBox.question(
                self, "验证警告",
                f"发现 {summary['errors']} 个错误和 {summary['warnings']} 个警告。\n"
                "建议先修复这些问题再导出。\n\n是否仍要继续导出？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.No:
                # 显示验证对话框
                from .validation_dialog import ValidationDialog
                validation_dialog = ValidationDialog(self)
                validation_dialog.set_validation_results(results, annotations)
                validation_dialog.fix_requested.connect(self.fix_annotation_issue)
                validation_dialog.exec_()
                return

        # 使用增强的导出对话框
        from .export_import_dialog import ExportImportDialog

        dialog = ExportImportDialog(self, mode="export")
        dialog.set_annotations(annotations)
        dialog.fps = self.fps

        dialog.exec_()

    def load_annotations(self, file_path):
        """加载标注数据"""
        try:
            import json
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # 清除现有时间窗口
            self.time_windows.clear()

            # 加载时间窗口
            for annotation in data.get("annotations", []):
                window_id = annotation.get("window_id")
                if not window_id:
                    window_id = self._generate_window_id()
                else:
                    # 确保window_id_counter不会产生重复ID
                    self.window_id_counter = max(self.window_id_counter, window_id)

                window = [
                    annotation["start_frame"],
                    annotation["end_frame"],
                    annotation.get("description", ""),
                    window_id
                ]
                self.time_windows.append(window)
                self.create_window_segment(window)

            print(f"从 {file_path} 加载了 {len(self.time_windows)} 个时间窗口")
            return True

        except Exception as e:
            print(f"加载标注数据失败: {e}")
            return False

    def load_annotations_dialog(self):
        """显示加载标注数据的对话框（使用增强的导入对话框）"""
        from .export_import_dialog import ExportImportDialog
        from PyQt5.QtWidgets import QMessageBox

        dialog = ExportImportDialog(self, mode="import")

        if dialog.exec_() == dialog.Accepted:
            imported_annotations = dialog.get_imported_annotations()

            if imported_annotations:
                # 清除现有时间窗口
                self.time_windows.clear()

                # 加载新的时间窗口
                for annotation in imported_annotations:
                    window_id = annotation.get("window_id")
                    if not window_id:
                        window_id = self._generate_window_id()
                    else:
                        # 确保window_id_counter不会产生重复ID
                        self.window_id_counter = max(self.window_id_counter, window_id)

                    window = [
                        annotation.get("start_frame", 0),
                        annotation.get("end_frame", 0),
                        annotation.get("description", ""),
                        window_id
                    ]
                    self.time_windows.append(window)
                    self.create_window_segment(window)

                QMessageBox.information(self, "成功", f"成功导入 {len(imported_annotations)} 个标注")
                print(f"成功导入 {len(imported_annotations)} 个时间窗口")

    def clear_all_windows(self):
        """清除所有时间窗口"""
        if not self.time_windows:
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.information(self, "提示", "没有时间窗口需要清除")
            return

        from PyQt5.QtWidgets import QMessageBox
        reply = QMessageBox.question(
            self, "确认清除",
            f"确定要清除所有 {len(self.time_windows)} 个时间窗口吗？此操作不可撤销。",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # 清除所有时间窗口
            self.time_windows.clear()

            # 清除所有时间轴上的窗口段
            for timeline in self.timelines:
                timeline.segments = [seg for seg in timeline.segments
                                   if not hasattr(seg, 'window_id')]
                timeline.update()

            print("已清除所有时间窗口")

    def keyPressEvent(self, event):
        """处理键盘事件"""
        from PyQt5.QtCore import Qt

        key = event.key()
        modifiers = event.modifiers()

        # 空格键：播放/暂停
        if key == Qt.Key_Space:
            self.toggle_play()
            event.accept()
            return

        # 左箭头：上一帧
        elif key == Qt.Key_Left:
            if modifiers == Qt.ControlModifier:
                self.jump_backward()  # Ctrl+左箭头：快退
            else:
                self.prev_frame()  # 左箭头：上一帧
            event.accept()
            return

        # 右箭头：下一帧
        elif key == Qt.Key_Right:
            if modifiers == Qt.ControlModifier:
                self.jump_forward()  # Ctrl+右箭头：快进
            else:
                self.next_frame()  # 右箭头：下一帧
            event.accept()
            return

        # Home键：跳到开始
        elif key == Qt.Key_Home:
            self.set_current_frame(0)
            event.accept()
            return

        # End键：跳到结束
        elif key == Qt.Key_End:
            self.set_current_frame(self.total_frames - 1)
            event.accept()
            return

        # 数字键：快速跳转到百分比位置
        elif Qt.Key_0 <= key <= Qt.Key_9:
            percentage = (key - Qt.Key_0) * 10  # 0-90%
            if percentage == 0:
                percentage = 100  # 0键表示100%
            frame = int((percentage / 100.0) * (self.total_frames - 1))
            self.set_current_frame(frame)
            event.accept()
            return

        # 其他键交给父类处理
        super().keyPressEvent(event)

    def validate_annotations(self):
        """验证标注数据"""
        if not self.time_windows:
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.information(self, "提示", "没有标注数据需要验证")
            return

        # 准备验证数据
        annotations = []
        for window in self.time_windows:
            start, end, description = window[0], window[1], window[2]
            window_id = window[3] if len(window) > 3 else None

            annotation = {
                "start_frame": start,
                "end_frame": end,
                "description": description,
                "duration_frames": end - start + 1
            }

            if window_id:
                annotation["window_id"] = window_id

            annotations.append(annotation)

        # 执行验证
        from ..utils.annotation_validator import AnnotationValidator
        from .validation_dialog import ValidationDialog

        validator = AnnotationValidator()
        results = validator.validate_annotations(annotations, self.total_frames)

        # 显示验证结果
        dialog = ValidationDialog(self)
        dialog.set_validation_results(results, annotations)

        # 连接修复信号
        dialog.fix_requested.connect(self.fix_annotation_issue)

        dialog.exec_()

    def fix_annotation_issue(self, annotation_index: int, field: str):
        """修复标注问题"""
        if annotation_index >= len(self.time_windows):
            return

        window = self.time_windows[annotation_index]

        # 根据字段类型进行自动修复
        if field == "description":
            # 如果描述为空，提供默认描述
            if not window[2] or not window[2].strip():
                window[2] = f"动作 {annotation_index + 1}"
                print(f"自动修复: 为标注 {annotation_index + 1} 添加默认描述")

        elif field == "start_frame" or field == "end_frame":
            # 修复时间范围问题
            start, end = window[0], window[1]
            if start >= end:
                # 调整结束帧
                window[1] = start + max(1, (end - start) // 2)
                print(f"自动修复: 调整标注 {annotation_index + 1} 的时间范围")

        # 更新时间轴段
        self._update_window_segment(window)

        from PyQt5.QtWidgets import QMessageBox
        QMessageBox.information(self, "修复完成", f"已修复标注 {annotation_index + 1} 的问题")

    def get_annotation_quality_score(self) -> float:
        """获取标注质量评分"""
        if not self.time_windows:
            return 0.0

        # 准备验证数据
        annotations = []
        for window in self.time_windows:
            start, end, description = window[0], window[1], window[2]
            annotations.append({
                "start_frame": start,
                "end_frame": end,
                "description": description,
                "duration_frames": end - start + 1
            })

        # 执行验证
        from ..utils.annotation_validator import AnnotationValidator

        validator = AnnotationValidator()
        results = validator.validate_annotations(annotations, self.total_frames)
        summary = validator.get_validation_summary(results)

        # 计算质量评分 (0-100)
        total_issues = summary['total_issues']
        errors = summary['errors']
        warnings = summary['warnings']

        if total_issues == 0:
            return 100.0

        # 错误权重更高
        penalty = errors * 10 + warnings * 5 + summary['info'] * 1
        max_penalty = len(annotations) * 15  # 假设每个标注最多15分扣分

        score = max(0, 100 - (penalty / max_penalty * 100))
        return round(score, 1)

    def update_quality_indicator(self):
        """更新质量指示器"""
        if not self.time_windows:
            self.quality_label.setText("质量: --")
            self.quality_label.setStyleSheet("font-weight: bold; min-width: 80px; font-size: 11px; color: #666;")
            return

        try:
            score = self.get_annotation_quality_score()
            self.quality_label.setText(f"质量: {score:.0f}")

            # 根据分数设置颜色
            if score >= 90:
                color = "#4CAF50"  # 绿色 - 优秀
            elif score >= 70:
                color = "#FF9800"  # 橙色 - 良好
            elif score >= 50:
                color = "#F44336"  # 红色 - 需要改进
            else:
                color = "#9C27B0"  # 紫色 - 较差

            self.quality_label.setStyleSheet(f"font-weight: bold; min-width: 80px; font-size: 11px; color: {color};")

        except Exception as e:
            print(f"更新质量指示器失败: {e}")
            self.quality_label.setText("质量: 错误")
            self.quality_label.setStyleSheet("font-weight: bold; min-width: 80px; font-size: 11px; color: #F44336;")