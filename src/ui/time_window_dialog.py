# -*- coding: utf-8 -*-
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                             QSpinBox, QTextEdit, QPushButton, QFormLayout,
                             QGroupBox, QSlider, QCheckBox, QMessageBox)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont


class TimeWindowDialog(QDialog):
    """时间窗口编辑对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("编辑时间窗口")
        self.setModal(True)
        self.resize(500, 400)
        
        # 获取父窗口的总帧数
        self.total_frames = getattr(parent, 'total_frames', 100)
        
        self.setup_ui()
        self.connect_signals()
        
    def setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)
        
        # 时间范围设置组
        time_group = QGroupBox("时间范围设置")
        time_layout = QFormLayout(time_group)
        
        # 起始帧
        self.start_spinbox = QSpinBox()
        self.start_spinbox.setRange(0, self.total_frames - 1)
        self.start_spinbox.setSuffix(" 帧")
        time_layout.addRow("起始帧:", self.start_spinbox)
        
        # 结束帧
        self.end_spinbox = QSpinBox()
        self.end_spinbox.setRange(0, self.total_frames - 1)
        self.end_spinbox.setSuffix(" 帧")
        time_layout.addRow("结束帧:", self.end_spinbox)
        
        # 持续时间显示
        self.duration_label = QLabel("持续时间: 0 帧")
        time_layout.addRow("", self.duration_label)
        
        # 范围滑块
        slider_layout = QHBoxLayout()
        self.range_slider_start = QSlider(Qt.Horizontal)
        self.range_slider_start.setRange(0, self.total_frames - 1)
        self.range_slider_end = QSlider(Qt.Horizontal)
        self.range_slider_end.setRange(0, self.total_frames - 1)
        
        slider_layout.addWidget(QLabel("起始:"))
        slider_layout.addWidget(self.range_slider_start)
        slider_layout.addWidget(QLabel("结束:"))
        slider_layout.addWidget(self.range_slider_end)
        
        time_layout.addRow("快速调整:", slider_layout)
        
        layout.addWidget(time_group)
        
        # 动作描述组
        desc_group = QGroupBox("动作描述")
        desc_layout = QVBoxLayout(desc_group)

        self.description_edit = QTextEdit()
        self.description_edit.setMaximumHeight(120)
        self.description_edit.setPlaceholderText("请输入动作描述...")

        # 设置字体
        font = QFont()
        font.setPointSize(10)
        self.description_edit.setFont(font)

        desc_layout.addWidget(self.description_edit)

        # 字符计数和高级编辑按钮
        desc_control_layout = QHBoxLayout()
        self.char_count_label = QLabel("字符数: 0")

        self.advanced_edit_button = QPushButton("高级编辑")
        self.advanced_edit_button.clicked.connect(self.open_advanced_editor)
        self.advanced_edit_button.setToolTip("打开高级动作描述编辑器")

        desc_control_layout.addWidget(self.char_count_label)
        desc_control_layout.addStretch()
        desc_control_layout.addWidget(self.advanced_edit_button)

        desc_layout.addLayout(desc_control_layout)

        layout.addWidget(desc_group)
        
        # 验证选项
        validation_group = QGroupBox("验证选项")
        validation_layout = QVBoxLayout(validation_group)
        
        self.require_description_checkbox = QCheckBox("要求填写描述")
        self.require_description_checkbox.setChecked(True)
        validation_layout.addWidget(self.require_description_checkbox)
        
        layout.addWidget(validation_group)
        
        # 按钮
        button_layout = QHBoxLayout()
        
        self.ok_button = QPushButton("确定")
        self.ok_button.setDefault(True)
        self.cancel_button = QPushButton("取消")
        
        button_layout.addStretch()
        button_layout.addWidget(self.ok_button)
        button_layout.addWidget(self.cancel_button)
        
        layout.addLayout(button_layout)
        
    def connect_signals(self):
        """连接信号"""
        # 数值同步
        self.start_spinbox.valueChanged.connect(self.on_start_changed)
        self.end_spinbox.valueChanged.connect(self.on_end_changed)
        self.range_slider_start.valueChanged.connect(self.start_spinbox.setValue)
        self.range_slider_end.valueChanged.connect(self.end_spinbox.setValue)
        
        # 描述变化
        self.description_edit.textChanged.connect(self.update_char_count)
        
        # 按钮
        self.ok_button.clicked.connect(self.accept_with_validation)
        self.cancel_button.clicked.connect(self.reject)
        
    def on_start_changed(self, value):
        """起始帧变化时的处理"""
        self.range_slider_start.setValue(value)
        
        # 确保结束帧不小于起始帧
        if self.end_spinbox.value() < value:
            self.end_spinbox.setValue(value)
            
        self.update_duration()
        
    def on_end_changed(self, value):
        """结束帧变化时的处理"""
        self.range_slider_end.setValue(value)
        
        # 确保起始帧不大于结束帧
        if self.start_spinbox.value() > value:
            self.start_spinbox.setValue(value)
            
        self.update_duration()
        
    def update_duration(self):
        """更新持续时间显示"""
        duration = self.end_spinbox.value() - self.start_spinbox.value() + 1
        self.duration_label.setText(f"持续时间: {duration} 帧")
        
    def update_char_count(self):
        """更新字符计数"""
        text = self.description_edit.toPlainText()
        count = len(text)
        self.char_count_label.setText(f"字符数: {count}")
        
    def accept_with_validation(self):
        """带验证的确认"""
        # 验证描述
        if self.require_description_checkbox.isChecked():
            description = self.description_edit.toPlainText().strip()
            if not description:
                QMessageBox.warning(self, "验证失败", "请填写动作描述")
                return
                
        # 验证时间范围
        if self.start_spinbox.value() >= self.end_spinbox.value():
            QMessageBox.warning(self, "验证失败", "结束帧必须大于起始帧")
            return
            
        self.accept()
        
    def set_window_data(self, start_frame, end_frame, description):
        """设置窗口数据"""
        self.start_spinbox.setValue(start_frame)
        self.end_spinbox.setValue(end_frame)
        self.description_edit.setPlainText(description)
        
        # 同步滑块
        self.range_slider_start.setValue(start_frame)
        self.range_slider_end.setValue(end_frame)
        
        self.update_duration()
        self.update_char_count()
        
    def open_advanced_editor(self):
        """打开高级动作描述编辑器"""
        from .action_description_dialog import ActionDescriptionDialog

        dialog = ActionDescriptionDialog(
            self,
            self.start_spinbox.value(),
            self.end_spinbox.value(),
            self.description_edit.toPlainText()
        )

        if dialog.exec_() == dialog.Accepted:
            new_description = dialog.get_description()
            self.description_edit.setPlainText(new_description)
            self.update_char_count()

    def get_window_data(self):
        """获取窗口数据"""
        return (
            self.start_spinbox.value(),
            self.end_spinbox.value(),
            self.description_edit.toPlainText().strip()
        )
