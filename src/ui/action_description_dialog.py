# -*- coding: utf-8 -*-
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                             QTextEdit, QPushButton, QFormLayout, QGroupBox, 
                             QComboBox, QListWidget, QListWidgetItem, QSplitter,
                             QCheckBox, QSpinBox, QTabWidget, QWidget, QMessageBox)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont


class ActionDescriptionDialog(QDialog):
    """动作描述编辑对话框"""
    
    def __init__(self, parent=None, start_frame=0, end_frame=0, initial_description=""):
        super().__init__(parent)
        self.setWindowTitle("编辑动作描述")
        self.setModal(True)
        self.resize(700, 500)
        
        self.start_frame = start_frame
        self.end_frame = end_frame
        self.initial_description = initial_description
        
        # 预定义的动作类别和常用描述
        self.action_categories = {
            "基本动作": ["走路", "跑步", "站立", "坐下", "躺下", "蹲下"],
            "手部动作": ["拿取", "放下", "推", "拉", "指向", "挥手", "鼓掌"],
            "交互动作": ["说话", "听", "看", "触摸", "握手", "拥抱"],
            "工作动作": ["写字", "打字", "画画", "切割", "组装", "操作设备"],
            "运动动作": ["跳跃", "踢", "投掷", "接球", "游泳", "骑车"],
            "其他": ["思考", "等待", "休息", "清洁", "整理"]
        }
        
        self.setup_ui()
        self.connect_signals()
        
        # 设置初始描述
        if initial_description:
            self.description_edit.setPlainText(initial_description)
            
    def setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)
        
        # 标题信息
        info_label = QLabel(f"编辑帧 {self.start_frame} - {self.end_frame} 的动作描述")
        info_label.setFont(QFont("Arial", 12, QFont.Bold))
        info_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(info_label)
        
        # 创建标签页
        tab_widget = QTabWidget()
        layout.addWidget(tab_widget)
        
        # 手动输入标签页
        manual_tab = QWidget()
        self.setup_manual_input_tab(manual_tab)
        tab_widget.addTab(manual_tab, "手动输入")
        
        # 快速选择标签页
        quick_tab = QWidget()
        self.setup_quick_selection_tab(quick_tab)
        tab_widget.addTab(quick_tab, "快速选择")
        
        # 模板标签页
        template_tab = QWidget()
        self.setup_template_tab(template_tab)
        tab_widget.addTab(template_tab, "描述模板")
        
        # 按钮区域
        self.setup_button_area(layout)
        
    def setup_manual_input_tab(self, parent):
        """设置手动输入标签页"""
        layout = QVBoxLayout(parent)
        
        # 描述输入区域
        desc_group = QGroupBox("动作描述")
        desc_layout = QVBoxLayout(desc_group)
        
        self.description_edit = QTextEdit()
        self.description_edit.setPlaceholderText("请详细描述在此时间段内发生的动作...")
        self.description_edit.setFont(QFont("Arial", 11))
        desc_layout.addWidget(self.description_edit)
        
        # 字符计数和提示
        info_layout = QHBoxLayout()
        self.char_count_label = QLabel("字符数: 0")
        self.word_count_label = QLabel("词数: 0")
        info_layout.addWidget(self.char_count_label)
        info_layout.addWidget(self.word_count_label)
        info_layout.addStretch()
        
        desc_layout.addLayout(info_layout)
        layout.addWidget(desc_group)
        
        # 描述质量检查
        quality_group = QGroupBox("描述质量检查")
        quality_layout = QVBoxLayout(quality_group)
        
        self.check_completeness = QCheckBox("包含主要动作")
        self.check_details = QCheckBox("包含动作细节")
        self.check_context = QCheckBox("包含环境上下文")
        
        quality_layout.addWidget(self.check_completeness)
        quality_layout.addWidget(self.check_details)
        quality_layout.addWidget(self.check_context)
        
        layout.addWidget(quality_group)
        
    def setup_quick_selection_tab(self, parent):
        """设置快速选择标签页"""
        layout = QVBoxLayout(parent)
        
        # 分类选择
        category_layout = QHBoxLayout()
        category_layout.addWidget(QLabel("动作类别:"))
        
        self.category_combo = QComboBox()
        self.category_combo.addItems(list(self.action_categories.keys()))
        category_layout.addWidget(self.category_combo)
        category_layout.addStretch()
        
        layout.addLayout(category_layout)
        
        # 动作列表
        self.action_list = QListWidget()
        self.action_list.setSelectionMode(QListWidget.MultiSelection)
        self.populate_action_list()
        layout.addWidget(self.action_list)
        
        # 操作按钮
        action_button_layout = QHBoxLayout()
        
        add_selected_btn = QPushButton("添加选中动作")
        add_selected_btn.clicked.connect(self.add_selected_actions)
        
        clear_selection_btn = QPushButton("清除选择")
        clear_selection_btn.clicked.connect(self.action_list.clearSelection)
        
        action_button_layout.addWidget(add_selected_btn)
        action_button_layout.addWidget(clear_selection_btn)
        action_button_layout.addStretch()
        
        layout.addLayout(action_button_layout)
        
    def setup_template_tab(self, parent):
        """设置模板标签页"""
        layout = QVBoxLayout(parent)
        
        # 模板选择
        template_group = QGroupBox("描述模板")
        template_layout = QVBoxLayout(template_group)
        
        self.template_list = QListWidget()
        templates = [
            "人物在{位置}{动作}，持续{时长}",
            "主体执行{主要动作}，同时{次要动作}",
            "从{起始状态}转换到{结束状态}",
            "使用{工具/物品}进行{动作}",
            "与{对象}进行{交互类型}"
        ]
        
        for template in templates:
            self.template_list.addItem(template)
            
        template_layout.addWidget(self.template_list)
        
        use_template_btn = QPushButton("使用选中模板")
        use_template_btn.clicked.connect(self.use_selected_template)
        template_layout.addWidget(use_template_btn)
        
        layout.addWidget(template_group)
        
        # 自定义模板
        custom_group = QGroupBox("自定义模板")
        custom_layout = QVBoxLayout(custom_group)
        
        self.custom_template_edit = QTextEdit()
        self.custom_template_edit.setMaximumHeight(80)
        self.custom_template_edit.setPlaceholderText("输入自定义模板，使用{}标记可替换部分...")
        custom_layout.addWidget(self.custom_template_edit)
        
        save_template_btn = QPushButton("保存为模板")
        save_template_btn.clicked.connect(self.save_custom_template)
        custom_layout.addWidget(save_template_btn)
        
        layout.addWidget(custom_group)
        
    def setup_button_area(self, layout):
        """设置按钮区域"""
        button_layout = QHBoxLayout()
        
        # 预览区域
        preview_group = QGroupBox("描述预览")
        preview_layout = QVBoxLayout(preview_group)
        
        self.preview_label = QLabel("描述预览将在这里显示...")
        self.preview_label.setWordWrap(True)
        self.preview_label.setStyleSheet("""
            QLabel {
                background-color: #f5f5f5;
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 8px;
                min-height: 60px;
            }
        """)
        preview_layout.addWidget(self.preview_label)
        
        layout.addWidget(preview_group)
        
        # 主要按钮
        main_button_layout = QHBoxLayout()
        
        self.ok_button = QPushButton("确定")
        self.ok_button.setDefault(True)
        self.ok_button.setMinimumHeight(35)
        
        cancel_button = QPushButton("取消")
        cancel_button.setMinimumHeight(35)
        
        validate_button = QPushButton("验证描述")
        validate_button.setMinimumHeight(35)
        validate_button.clicked.connect(self.validate_description)
        
        main_button_layout.addWidget(validate_button)
        main_button_layout.addStretch()
        main_button_layout.addWidget(self.ok_button)
        main_button_layout.addWidget(cancel_button)
        
        layout.addLayout(main_button_layout)
        
    def connect_signals(self):
        """连接信号"""
        # 文本变化
        self.description_edit.textChanged.connect(self.update_counts)
        self.description_edit.textChanged.connect(self.update_preview)
        
        # 类别变化
        self.category_combo.currentTextChanged.connect(self.populate_action_list)
        
        # 按钮
        self.ok_button.clicked.connect(self.accept_with_validation)
        
    def populate_action_list(self):
        """填充动作列表"""
        self.action_list.clear()
        category = self.category_combo.currentText()
        actions = self.action_categories.get(category, [])
        
        for action in actions:
            self.action_list.addItem(action)
            
    def add_selected_actions(self):
        """添加选中的动作到描述"""
        selected_items = self.action_list.selectedItems()
        if not selected_items:
            return
            
        actions = [item.text() for item in selected_items]
        current_text = self.description_edit.toPlainText()
        
        if current_text:
            new_text = current_text + "，" + "、".join(actions)
        else:
            new_text = "、".join(actions)
            
        self.description_edit.setPlainText(new_text)
        
    def use_selected_template(self):
        """使用选中的模板"""
        current_item = self.template_list.currentItem()
        if current_item:
            template = current_item.text()
            self.description_edit.setPlainText(template)
            
    def save_custom_template(self):
        """保存自定义模板"""
        template = self.custom_template_edit.toPlainText().strip()
        if template:
            self.template_list.addItem(template)
            self.custom_template_edit.clear()
            QMessageBox.information(self, "成功", "模板已保存")
            
    def update_counts(self):
        """更新字符和词数统计"""
        text = self.description_edit.toPlainText()
        char_count = len(text)
        word_count = len(text.split()) if text.strip() else 0
        
        self.char_count_label.setText(f"字符数: {char_count}")
        self.word_count_label.setText(f"词数: {word_count}")
        
    def update_preview(self):
        """更新预览"""
        text = self.description_edit.toPlainText().strip()
        if text:
            self.preview_label.setText(f"帧 {self.start_frame}-{self.end_frame}: {text}")
            self.preview_label.setStyleSheet("""
                QLabel {
                    background-color: #e8f5e8;
                    border: 1px solid #4caf50;
                    border-radius: 4px;
                    padding: 8px;
                    min-height: 60px;
                    color: #2e7d32;
                }
            """)
        else:
            self.preview_label.setText("描述预览将在这里显示...")
            self.preview_label.setStyleSheet("""
                QLabel {
                    background-color: #f5f5f5;
                    border: 1px solid #ddd;
                    border-radius: 4px;
                    padding: 8px;
                    min-height: 60px;
                    color: #666;
                }
            """)
            
    def validate_description(self):
        """验证描述质量"""
        text = self.description_edit.toPlainText().strip()
        
        if not text:
            QMessageBox.warning(self, "验证失败", "请输入动作描述")
            return
            
        # 简单的质量检查
        issues = []
        
        if len(text) < 10:
            issues.append("描述过于简短")
            
        if not any(action in text for category in self.action_categories.values() for action in category):
            issues.append("建议包含具体的动作词汇")
            
        if issues:
            QMessageBox.warning(self, "描述建议", "建议改进：\n" + "\n".join(issues))
        else:
            QMessageBox.information(self, "验证通过", "描述质量良好！")
            
    def accept_with_validation(self):
        """带验证的确认"""
        text = self.description_edit.toPlainText().strip()
        if not text:
            QMessageBox.warning(self, "验证失败", "请输入动作描述")
            return
            
        self.accept()
        
    def get_description(self):
        """获取描述"""
        return self.description_edit.toPlainText().strip()
