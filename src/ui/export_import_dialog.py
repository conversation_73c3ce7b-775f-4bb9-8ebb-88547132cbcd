# -*- coding: utf-8 -*-
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                             QPushButton, QComboBox, QGroupBox, QFormLayout,
                             QSpinBox, QCheckBox, QTextEdit, QFileDialog,
                             QMessageBox, QTabWidget, QWidget, QProgressBar)
from PyQt5.QtCore import Qt, QThread, pyqtSignal
from PyQt5.QtGui import QFont
import os


class ExportImportDialog(QDialog):
    """导出/导入标注数据对话框"""
    
    def __init__(self, parent=None, mode="export"):
        super().__init__(parent)
        self.mode = mode  # "export" 或 "import"
        self.annotations = []
        self.fps = 25.0
        
        self.setWindowTitle("导出标注数据" if mode == "export" else "导入标注数据")
        self.setModal(True)
        self.resize(600, 500)
        
        self.setup_ui()
        self.connect_signals()
        
    def setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)
        
        if self.mode == "export":
            self.setup_export_ui(layout)
        else:
            self.setup_import_ui(layout)
            
        # 按钮区域
        self.setup_button_area(layout)
        
    def setup_export_ui(self, layout):
        """设置导出界面"""
        # 格式选择
        format_group = QGroupBox("导出格式")
        format_layout = QFormLayout(format_group)
        
        self.format_combo = QComboBox()
        self.format_combo.addItems([
            "JSON (推荐)",
            "CSV (表格)",
            "XML (结构化)",
            "TXT (纯文本)",
            "SRT (字幕)",
            "VTT (Web字幕)"
        ])
        format_layout.addRow("格式:", self.format_combo)
        
        layout.addWidget(format_group)
        
        # 导出选项
        options_group = QGroupBox("导出选项")
        options_layout = QFormLayout(options_group)
        
        # FPS设置（用于时间转换）
        self.fps_spinbox = QSpinBox()
        self.fps_spinbox.setRange(1, 120)
        self.fps_spinbox.setValue(25)
        self.fps_spinbox.setSuffix(" FPS")
        options_layout.addRow("视频帧率:", self.fps_spinbox)
        
        # 包含元数据
        self.include_metadata_checkbox = QCheckBox("包含导出元数据")
        self.include_metadata_checkbox.setChecked(True)
        options_layout.addRow("", self.include_metadata_checkbox)
        
        # 压缩输出
        self.compress_checkbox = QCheckBox("压缩输出文件")
        self.compress_checkbox.setChecked(False)
        options_layout.addRow("", self.compress_checkbox)
        
        layout.addWidget(options_group)
        
        # 预览区域
        preview_group = QGroupBox("导出预览")
        preview_layout = QVBoxLayout(preview_group)
        
        self.preview_text = QTextEdit()
        self.preview_text.setMaximumHeight(150)
        self.preview_text.setReadOnly(True)
        self.preview_text.setPlaceholderText("选择格式后将显示导出预览...")
        preview_layout.addWidget(self.preview_text)
        
        layout.addWidget(preview_group)
        
    def setup_import_ui(self, layout):
        """设置导入界面"""
        # 文件选择
        file_group = QGroupBox("选择文件")
        file_layout = QHBoxLayout(file_group)
        
        self.file_path_label = QLabel("未选择文件")
        self.browse_button = QPushButton("浏览...")
        self.browse_button.clicked.connect(self.browse_import_file)
        
        file_layout.addWidget(self.file_path_label, 1)
        file_layout.addWidget(self.browse_button)
        
        layout.addWidget(file_group)
        
        # 导入选项
        options_group = QGroupBox("导入选项")
        options_layout = QFormLayout(options_group)
        
        # 格式检测
        self.auto_detect_checkbox = QCheckBox("自动检测格式")
        self.auto_detect_checkbox.setChecked(True)
        options_layout.addRow("", self.auto_detect_checkbox)
        
        # 手动格式选择
        self.import_format_combo = QComboBox()
        self.import_format_combo.addItems(["JSON", "CSV", "XML"])
        self.import_format_combo.setEnabled(False)
        options_layout.addRow("格式:", self.import_format_combo)
        
        # 验证数据
        self.validate_checkbox = QCheckBox("验证导入数据")
        self.validate_checkbox.setChecked(True)
        options_layout.addRow("", self.validate_checkbox)
        
        # 合并模式
        self.merge_checkbox = QCheckBox("与现有数据合并")
        self.merge_checkbox.setChecked(False)
        options_layout.addRow("", self.merge_checkbox)
        
        layout.addWidget(options_group)
        
        # 预览区域
        preview_group = QGroupBox("导入预览")
        preview_layout = QVBoxLayout(preview_group)
        
        self.import_preview_text = QTextEdit()
        self.import_preview_text.setMaximumHeight(150)
        self.import_preview_text.setReadOnly(True)
        self.import_preview_text.setPlaceholderText("选择文件后将显示导入预览...")
        preview_layout.addWidget(self.import_preview_text)
        
        layout.addWidget(preview_group)
        
    def setup_button_area(self, layout):
        """设置按钮区域"""
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # 按钮
        button_layout = QHBoxLayout()
        
        if self.mode == "export":
            self.export_button = QPushButton("导出")
            self.export_button.setDefault(True)
            self.export_button.clicked.connect(self.export_annotations)
            button_layout.addWidget(self.export_button)
        else:
            self.import_button = QPushButton("导入")
            self.import_button.setDefault(True)
            self.import_button.setEnabled(False)
            self.import_button.clicked.connect(self.import_annotations)
            button_layout.addWidget(self.import_button)
        
        cancel_button = QPushButton("取消")
        cancel_button.clicked.connect(self.reject)
        button_layout.addWidget(cancel_button)
        
        layout.addLayout(button_layout)
        
    def connect_signals(self):
        """连接信号"""
        if self.mode == "export":
            self.format_combo.currentTextChanged.connect(self.update_export_preview)
            self.fps_spinbox.valueChanged.connect(self.update_export_preview)
        else:
            self.auto_detect_checkbox.toggled.connect(self.on_auto_detect_toggled)
            
    def on_auto_detect_toggled(self, checked):
        """自动检测格式切换"""
        self.import_format_combo.setEnabled(not checked)
        
    def browse_import_file(self):
        """浏览导入文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择导入文件", "",
            "所有支持的格式 (*.json *.csv *.xml);;JSON文件 (*.json);;CSV文件 (*.csv);;XML文件 (*.xml);;所有文件 (*)"
        )
        
        if file_path:
            self.file_path_label.setText(os.path.basename(file_path))
            self.file_path_label.setToolTip(file_path)
            self.selected_file_path = file_path
            self.import_button.setEnabled(True)
            self.preview_import_file()
            
    def preview_import_file(self):
        """预览导入文件"""
        try:
            from ..utils.annotation_exporter import AnnotationImporter
            
            importer = AnnotationImporter()
            annotations = importer.import_annotations(self.selected_file_path)
            
            if annotations:
                preview_text = f"找到 {len(annotations)} 个标注:\n\n"
                for i, ann in enumerate(annotations[:3]):  # 只显示前3个
                    preview_text += f"标注 {i+1}:\n"
                    preview_text += f"  帧范围: {ann.get('start_frame', 'N/A')} - {ann.get('end_frame', 'N/A')}\n"
                    preview_text += f"  描述: {ann.get('description', 'N/A')[:50]}...\n\n"
                
                if len(annotations) > 3:
                    preview_text += f"... 还有 {len(annotations) - 3} 个标注"
                
                self.import_preview_text.setPlainText(preview_text)
                self.preview_annotations = annotations
            else:
                self.import_preview_text.setPlainText("无法解析文件或文件为空")
                
        except Exception as e:
            self.import_preview_text.setPlainText(f"预览失败: {str(e)}")
            
    def update_export_preview(self):
        """更新导出预览"""
        if not self.annotations:
            self.preview_text.setPlaceholderText("没有标注数据可预览")
            return
            
        format_text = self.format_combo.currentText()
        format_type = format_text.split()[0].lower()
        
        try:
            from ..utils.annotation_exporter import AnnotationExporter
            
            exporter = AnnotationExporter()
            
            # 创建临时预览数据（只取前2个标注）
            preview_data = self.annotations[:2]
            
            if format_type == "json":
                import json
                preview = json.dumps({
                    "metadata": {"total_annotations": len(self.annotations)},
                    "annotations": preview_data
                }, ensure_ascii=False, indent=2)
            elif format_type == "csv":
                preview = "start_frame,end_frame,duration_frames,description\n"
                for ann in preview_data:
                    preview += f"{ann.get('start_frame', 0)},{ann.get('end_frame', 0)},{ann.get('duration_frames', 0)},\"{ann.get('description', '')}\"\n"
            elif format_type == "srt":
                preview = ""
                fps = self.fps_spinbox.value()
                for i, ann in enumerate(preview_data, 1):
                    start_time = exporter._frame_to_srt_time(ann.get('start_frame', 0), fps)
                    end_time = exporter._frame_to_srt_time(ann.get('end_frame', 0), fps)
                    preview += f"{i}\n{start_time} --> {end_time}\n{ann.get('description', '')}\n\n"
            else:
                preview = f"预览格式: {format_type}\n总标注数: {len(self.annotations)}"
                
            self.preview_text.setPlainText(preview)
            
        except Exception as e:
            self.preview_text.setPlainText(f"预览生成失败: {str(e)}")
            
    def export_annotations(self):
        """导出标注"""
        if not self.annotations:
            QMessageBox.warning(self, "警告", "没有标注数据可导出")
            return
            
        format_text = self.format_combo.currentText()
        format_type = format_text.split()[0].lower()
        
        # 选择保存文件
        filters = {
            "json": "JSON文件 (*.json)",
            "csv": "CSV文件 (*.csv)",
            "xml": "XML文件 (*.xml)",
            "txt": "文本文件 (*.txt)",
            "srt": "SRT字幕文件 (*.srt)",
            "vtt": "VTT字幕文件 (*.vtt)"
        }
        
        file_path, _ = QFileDialog.getSaveFileName(
            self, "保存导出文件", f"annotations.{format_type}",
            f"{filters.get(format_type, '所有文件 (*)')}"
        )
        
        if file_path:
            try:
                from ..utils.annotation_exporter import AnnotationExporter
                
                exporter = AnnotationExporter()
                success = exporter.export(
                    self.annotations, 
                    file_path, 
                    format_type,
                    fps=self.fps_spinbox.value()
                )
                
                if success:
                    QMessageBox.information(self, "成功", f"标注数据已导出到:\n{file_path}")
                    self.accept()
                else:
                    QMessageBox.warning(self, "失败", "导出失败")
                    
            except Exception as e:
                QMessageBox.critical(self, "错误", f"导出时发生错误:\n{str(e)}")
                
    def import_annotations(self):
        """导入标注"""
        try:
            from ..utils.annotation_exporter import AnnotationImporter
            
            importer = AnnotationImporter()
            format_type = None if self.auto_detect_checkbox.isChecked() else self.import_format_combo.currentText().lower()
            
            annotations = importer.import_annotations(self.selected_file_path, format_type)
            
            if annotations:
                self.imported_annotations = annotations
                QMessageBox.information(self, "成功", f"成功导入 {len(annotations)} 个标注")
                self.accept()
            else:
                QMessageBox.warning(self, "失败", "导入失败或文件为空")
                
        except Exception as e:
            QMessageBox.critical(self, "错误", f"导入时发生错误:\n{str(e)}")
            
    def set_annotations(self, annotations):
        """设置要导出的标注数据"""
        self.annotations = annotations
        if self.mode == "export":
            self.update_export_preview()
            
    def get_imported_annotations(self):
        """获取导入的标注数据"""
        return getattr(self, 'imported_annotations', [])
