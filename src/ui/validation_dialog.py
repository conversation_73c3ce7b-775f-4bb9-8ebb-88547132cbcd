# -*- coding: utf-8 -*-
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                             QPushButton, QTreeWidget, QTreeWidgetItem, QGroupBox,
                             QTextEdit, QSplitter, QHeaderView, QProgressBar,
                             QCheckBox, QComboBox, QMessageBox)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QIcon, QColor, QPalette
from typing import List, Dict, Any
import sys
import os

# 添加父目录到路径以便导入
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from utils.annotation_validator import ValidationResult, ValidationLevel


class ValidationDialog(QDialog):
    """标注验证结果对话框"""
    
    fix_requested = pyqtSignal(int, str)  # 请求修复信号 (annotation_index, field)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("标注验证结果")
        self.setModal(True)
        self.resize(800, 600)
        
        self.validation_results = []
        self.annotations = []
        
        self.setup_ui()
        self.connect_signals()
        
    def setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)
        
        # 摘要区域
        self.setup_summary_area(layout)
        
        # 主要内容区域
        main_splitter = QSplitter(Qt.Horizontal)
        layout.addWidget(main_splitter)
        
        # 左侧：问题列表
        self.setup_issues_tree(main_splitter)
        
        # 右侧：详细信息
        self.setup_details_area(main_splitter)
        
        # 设置分割器比例
        main_splitter.setSizes([400, 400])
        
        # 按钮区域
        self.setup_button_area(layout)
        
    def setup_summary_area(self, layout):
        """设置摘要区域"""
        summary_group = QGroupBox("验证摘要")
        summary_layout = QHBoxLayout(summary_group)
        
        # 统计标签
        self.total_label = QLabel("总问题: 0")
        self.errors_label = QLabel("错误: 0")
        self.warnings_label = QLabel("警告: 0")
        self.info_label = QLabel("信息: 0")
        
        # 设置标签样式
        self.errors_label.setStyleSheet("color: #d32f2f; font-weight: bold;")
        self.warnings_label.setStyleSheet("color: #f57c00; font-weight: bold;")
        self.info_label.setStyleSheet("color: #1976d2; font-weight: bold;")
        
        summary_layout.addWidget(self.total_label)
        summary_layout.addWidget(QLabel("|"))
        summary_layout.addWidget(self.errors_label)
        summary_layout.addWidget(QLabel("|"))
        summary_layout.addWidget(self.warnings_label)
        summary_layout.addWidget(QLabel("|"))
        summary_layout.addWidget(self.info_label)
        summary_layout.addStretch()
        
        # 过滤选项
        filter_layout = QHBoxLayout()
        filter_layout.addWidget(QLabel("显示:"))
        
        self.filter_combo = QComboBox()
        self.filter_combo.addItems(["全部", "仅错误", "仅警告", "仅信息"])
        filter_layout.addWidget(self.filter_combo)
        
        self.auto_fix_checkbox = QCheckBox("显示可自动修复的问题")
        filter_layout.addWidget(self.auto_fix_checkbox)
        
        summary_layout.addLayout(filter_layout)
        
        layout.addWidget(summary_group)
        
    def setup_issues_tree(self, parent):
        """设置问题列表树"""
        issues_group = QGroupBox("问题列表")
        issues_layout = QVBoxLayout(issues_group)
        
        self.issues_tree = QTreeWidget()
        self.issues_tree.setHeaderLabels(["级别", "问题描述", "位置"])
        self.issues_tree.setAlternatingRowColors(True)
        self.issues_tree.setRootIsDecorated(False)
        
        # 设置列宽
        header = self.issues_tree.header()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.Stretch)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        
        issues_layout.addWidget(self.issues_tree)
        parent.addWidget(issues_group)
        
    def setup_details_area(self, parent):
        """设置详细信息区域"""
        details_group = QGroupBox("详细信息")
        details_layout = QVBoxLayout(details_group)
        
        # 问题详情
        self.details_text = QTextEdit()
        self.details_text.setReadOnly(True)
        self.details_text.setMaximumHeight(200)
        details_layout.addWidget(self.details_text)
        
        # 建议和操作
        suggestion_group = QGroupBox("建议和操作")
        suggestion_layout = QVBoxLayout(suggestion_group)
        
        self.suggestion_text = QTextEdit()
        self.suggestion_text.setReadOnly(True)
        self.suggestion_text.setMaximumHeight(100)
        suggestion_layout.addWidget(self.suggestion_text)
        
        # 操作按钮
        action_layout = QHBoxLayout()
        
        self.fix_button = QPushButton("修复此问题")
        self.fix_button.setEnabled(False)
        self.fix_button.clicked.connect(self.fix_current_issue)
        
        self.ignore_button = QPushButton("忽略此问题")
        self.ignore_button.setEnabled(False)
        self.ignore_button.clicked.connect(self.ignore_current_issue)
        
        action_layout.addWidget(self.fix_button)
        action_layout.addWidget(self.ignore_button)
        action_layout.addStretch()
        
        suggestion_layout.addLayout(action_layout)
        details_layout.addWidget(suggestion_group)
        
        parent.addWidget(details_group)
        
    def setup_button_area(self, layout):
        """设置按钮区域"""
        button_layout = QHBoxLayout()
        
        # 批量操作
        self.fix_all_button = QPushButton("修复所有可修复问题")
        self.fix_all_button.clicked.connect(self.fix_all_issues)
        
        self.export_button = QPushButton("导出报告")
        self.export_button.clicked.connect(self.export_report)
        
        button_layout.addWidget(self.fix_all_button)
        button_layout.addWidget(self.export_button)
        button_layout.addStretch()
        
        # 关闭按钮
        close_button = QPushButton("关闭")
        close_button.clicked.connect(self.accept)
        button_layout.addWidget(close_button)
        
        layout.addLayout(button_layout)
        
    def connect_signals(self):
        """连接信号"""
        self.issues_tree.itemSelectionChanged.connect(self.on_selection_changed)
        self.filter_combo.currentTextChanged.connect(self.filter_issues)
        self.auto_fix_checkbox.toggled.connect(self.filter_issues)
        
    def set_validation_results(self, results: List[ValidationResult], 
                             annotations: List[Dict] = None):
        """设置验证结果"""
        self.validation_results = results
        self.annotations = annotations or []
        
        self.update_summary()
        self.populate_issues_tree()
        
    def update_summary(self):
        """更新摘要信息"""
        total = len(self.validation_results)
        errors = len([r for r in self.validation_results if r.level == ValidationLevel.ERROR])
        warnings = len([r for r in self.validation_results if r.level == ValidationLevel.WARNING])
        info = len([r for r in self.validation_results if r.level == ValidationLevel.INFO])
        
        self.total_label.setText(f"总问题: {total}")
        self.errors_label.setText(f"错误: {errors}")
        self.warnings_label.setText(f"警告: {warnings}")
        self.info_label.setText(f"信息: {info}")
        
    def populate_issues_tree(self):
        """填充问题列表"""
        self.issues_tree.clear()
        
        for i, result in enumerate(self.validation_results):
            if not self._should_show_result(result):
                continue
                
            item = QTreeWidgetItem()
            
            # 级别
            level_text = result.level.value.upper()
            item.setText(0, level_text)
            
            # 设置级别颜色
            if result.level == ValidationLevel.ERROR:
                item.setForeground(0, QColor("#d32f2f"))
            elif result.level == ValidationLevel.WARNING:
                item.setForeground(0, QColor("#f57c00"))
            else:
                item.setForeground(0, QColor("#1976d2"))
            
            # 问题描述
            item.setText(1, result.message)
            
            # 位置
            location = ""
            if result.annotation_index is not None:
                location = f"标注 {result.annotation_index + 1}"
                if result.field:
                    location += f" ({result.field})"
            item.setText(2, location)
            
            # 存储结果对象
            item.setData(0, Qt.UserRole, i)
            
            self.issues_tree.addTopLevelItem(item)
            
    def _should_show_result(self, result: ValidationResult) -> bool:
        """判断是否应该显示此结果"""
        filter_text = self.filter_combo.currentText()
        
        if filter_text == "仅错误" and result.level != ValidationLevel.ERROR:
            return False
        elif filter_text == "仅警告" and result.level != ValidationLevel.WARNING:
            return False
        elif filter_text == "仅信息" and result.level != ValidationLevel.INFO:
            return False
        
        if self.auto_fix_checkbox.isChecked():
            # 只显示可自动修复的问题
            return self._is_auto_fixable(result)
        
        return True
        
    def _is_auto_fixable(self, result: ValidationResult) -> bool:
        """判断问题是否可以自动修复"""
        # 定义可自动修复的问题类型
        auto_fixable_patterns = [
            "描述不能为空",
            "描述过短",
            "起始帧.*必须小于结束帧",
            ".*不能为负数"
        ]
        
        import re
        for pattern in auto_fixable_patterns:
            if re.search(pattern, result.message):
                return True
        
        return False
        
    def filter_issues(self):
        """过滤问题列表"""
        self.populate_issues_tree()
        
    def on_selection_changed(self):
        """处理选择变化"""
        current_item = self.issues_tree.currentItem()
        if not current_item:
            self.details_text.clear()
            self.suggestion_text.clear()
            self.fix_button.setEnabled(False)
            self.ignore_button.setEnabled(False)
            return
        
        result_index = current_item.data(0, Qt.UserRole)
        result = self.validation_results[result_index]
        
        # 显示详细信息
        details = f"级别: {result.level.value.upper()}\n"
        details += f"消息: {result.message}\n"
        
        if result.annotation_index is not None:
            details += f"标注索引: {result.annotation_index + 1}\n"
            
        if result.field:
            details += f"字段: {result.field}\n"
            
        self.details_text.setPlainText(details)
        
        # 显示建议
        suggestion = result.suggestion or "暂无具体建议"
        self.suggestion_text.setPlainText(suggestion)
        
        # 更新按钮状态
        self.fix_button.setEnabled(self._is_auto_fixable(result))
        self.ignore_button.setEnabled(True)
        
    def fix_current_issue(self):
        """修复当前问题"""
        current_item = self.issues_tree.currentItem()
        if not current_item:
            return
            
        result_index = current_item.data(0, Qt.UserRole)
        result = self.validation_results[result_index]
        
        if result.annotation_index is not None:
            self.fix_requested.emit(result.annotation_index, result.field or "")
            
    def ignore_current_issue(self):
        """忽略当前问题"""
        current_item = self.issues_tree.currentItem()
        if current_item:
            # 从列表中移除
            index = self.issues_tree.indexOfTopLevelItem(current_item)
            self.issues_tree.takeTopLevelItem(index)
            
    def fix_all_issues(self):
        """修复所有可修复的问题"""
        fixable_count = 0
        
        for result in self.validation_results:
            if self._is_auto_fixable(result) and result.annotation_index is not None:
                self.fix_requested.emit(result.annotation_index, result.field or "")
                fixable_count += 1
        
        if fixable_count > 0:
            QMessageBox.information(
                self, "修复完成", 
                f"已尝试修复 {fixable_count} 个问题，请重新验证查看结果"
            )
        else:
            QMessageBox.information(self, "提示", "没有找到可自动修复的问题")
            
    def export_report(self):
        """导出验证报告"""
        from PyQt5.QtWidgets import QFileDialog
        
        file_path, _ = QFileDialog.getSaveFileName(
            self, "导出验证报告", "validation_report.txt", 
            "文本文件 (*.txt);;所有文件 (*)"
        )
        
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write("标注验证报告\n")
                    f.write("=" * 50 + "\n\n")
                    
                    # 摘要
                    f.write("摘要:\n")
                    f.write(f"  总问题数: {len(self.validation_results)}\n")
                    f.write(f"  错误: {len([r for r in self.validation_results if r.level == ValidationLevel.ERROR])}\n")
                    f.write(f"  警告: {len([r for r in self.validation_results if r.level == ValidationLevel.WARNING])}\n")
                    f.write(f"  信息: {len([r for r in self.validation_results if r.level == ValidationLevel.INFO])}\n\n")
                    
                    # 详细问题
                    f.write("详细问题:\n")
                    f.write("-" * 30 + "\n")
                    
                    for i, result in enumerate(self.validation_results, 1):
                        f.write(f"{i}. [{result.level.value.upper()}] {result.message}\n")
                        if result.annotation_index is not None:
                            f.write(f"   位置: 标注 {result.annotation_index + 1}")
                            if result.field:
                                f.write(f" ({result.field})")
                            f.write("\n")
                        if result.suggestion:
                            f.write(f"   建议: {result.suggestion}\n")
                        f.write("\n")
                
                QMessageBox.information(self, "成功", f"验证报告已导出到: {file_path}")
                
            except Exception as e:
                QMessageBox.warning(self, "失败", f"导出失败: {str(e)}")
